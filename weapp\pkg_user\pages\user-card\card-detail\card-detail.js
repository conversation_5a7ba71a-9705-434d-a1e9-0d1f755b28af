// pkg_user/pages/card-detail/card-detail.js
const { request } = require('../../../../utils/request');

Page({
  data: {
    // 名片信息
    cardInfo: null,
    // 是否为预览模式
    isPreview: false,
    // 加载状态
    loading: true
  },

  onLoad(options) {
    const { id, data, preview } = options;
    
    if (preview && data) {
      // 预览模式
      try {
        const cardData = JSON.parse(decodeURIComponent(data));
        this.setData({
          cardInfo: cardData,
          isPreview: true,
          loading: false
        });
        
        wx.setNavigationBarTitle({
          title: '名片预览'
        });
      } catch (error) {
        console.error('解析预览数据失败:', error);
        wx.showToast({
          title: '数据错误',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } else if (id) {
      // 详情模式
      this.loadCardDetail(id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载名片详情
  async loadCardDetail(id) {
    try {
      this.setData({ loading: true });

      // 模拟API调用
      const result = await this.mockLoadCard(id);

      if (result.code === 200 && result.data) {
        this.setData({
          cardInfo: result.data,
          loading: false
        });

        wx.setNavigationBarTitle({
          title: result.data.name + '的名片'
        });
      } else {
        throw new Error(result.msg || '加载失败');
      }
    } catch (error) {
      console.error('加载名片详情失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 模拟加载名片
  mockLoadCard(id) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {
            cardId: id, // 名片ID
            company: '上海觅知房地产有限公司', // 公司名称
            position: '总经理', // 职位
            businessIntro: '专注于房地产开发与销售，提供专业的房产咨询服务，致力于为客户创造价值。', // 业务简介
            name: '觅知君', // 姓名
            gender: 1, // 性别(0-保密，1-男，2-女)
            phone: '123 4567 8910', // 电话
            address: '上海市浦东新区秀浦路11号', // 地址
            email: '<EMAIL>', // 邮箱
            website: 'www.51miz.com', // 网址
            wechat: 'juezhi_123', // 微信
            avatar: '/assets/images/bot-avatar.png', // 头像
            images: [ // 图片
              'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=Company+Image+1',
              'https://via.placeholder.com/400x300/50C878/FFFFFF?text=Company+Image+2'
            ],
            videos: [ // 视频
              'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
            ],
            remark: '更新于 2024-07-20 14:30', // 备注信息
            isPublic: 1, // 是否公开(0-否，1-是)
            updateTime: '2024-07-20 14:30:00', // 更新时间
            auditStatus: 1, // 审核状态(0-待审核，1-已通过，2-未通过)
            cardType: 'business', // 名片类型
            userId: '10086' // 用户ID
          }
        });
      }, 500);
    });
  },

  // 拨打电话
  onCallPhone() {
    const phone = this.data.cardInfo?.phone;
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone.replace(/\s/g, '') // 移除空格
      });
    }
  },

  // 发送邮件
  onSendEmail() {
    const email = this.data.cardInfo?.email;
    if (email) {
      wx.setClipboardData({
        data: email,
        success: () => {
          wx.showToast({
            title: '邮箱已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  // 复制微信号
  onCopyWechat() {
    const wechat = this.data.cardInfo?.wechat;
    if (wechat) {
      wx.setClipboardData({
        data: wechat,
        success: () => {
          wx.showToast({
            title: '微信号已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  // 查看网站
  onViewWebsite() {
    const website = this.data.cardInfo?.website;
    if (website) {
      wx.setClipboardData({
        data: website,
        success: () => {
          wx.showToast({
            title: '网址已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  // 查看地址
  onViewAddress() {
    const address = this.data.cardInfo?.address;
    if (address) {
      wx.setClipboardData({
        data: address,
        success: () => {
          wx.showToast({
            title: '地址已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  // 保存到通讯录
  onSaveContact() {
    const cardInfo = this.data.cardInfo;
    if (!cardInfo) return;

    wx.addPhoneContact({
      firstName: cardInfo.name,
      organization: cardInfo.company,
      title: cardInfo.position,
      mobilePhoneNumber: cardInfo.phone,
      email: cardInfo.email,
      url: cardInfo.website,
      success: () => {
        wx.showToast({
          title: '已保存到通讯录',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    });
  },

  // 分享名片
  onShareCard() {
    const cardInfo = this.data.cardInfo;
    if (!cardInfo) return;

    // 这里可以生成名片的分享链接或二维码
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  },

  // 编辑名片
  onEditCard() {
    if (this.data.isPreview) {
      wx.navigateBack();
      return;
    }

    const cardData = encodeURIComponent(JSON.stringify(this.data.cardInfo));
    wx.navigateTo({
      url: `/pkg_user/pages/edit-card/edit-card?data=${cardData}`
    });
  },

  // 预览图片
  onPreviewImage(e) {
    const src = e.currentTarget.dataset.src;
    const images = this.data.cardInfo?.images || [];

    wx.previewImage({
      current: src,
      urls: images
    });
  }
});
