<!--添加名片页面-->
<view class="add-card-container">
  <view class="form-container">
    <scroll-view class="form-scroll" scroll-y="true">
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="form-label">名片ID *</text>
          <input class="form-input" placeholder="请输入名片ID" value="{{formData.cardId}}" bindinput="onInputChange" data-field="cardId"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">公司名称 *</text>
          <input class="form-input" placeholder="请输入公司名称" value="{{formData.company}}" bindinput="onInputChange" data-field="company"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">职位</text>
          <input class="form-input" placeholder="请输入职位" value="{{formData.position}}" bindinput="onInputChange" data-field="position"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">姓名 *</text>
          <input class="form-input" placeholder="请输入姓名" value="{{formData.name}}" bindinput="onInputChange" data-field="name"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">性别</text>
          <picker class="form-picker" range="{{genderOptions}}" value="{{formData.gender}}" bindchange="onPickerChange" data-field="gender">
            <view class="picker-text">{{genderOptions[formData.gender]}}</view>
          </picker>
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">联系信息</view>
        
        <view class="form-item">
          <text class="form-label">电话</text>
          <input class="form-input" placeholder="请输入电话" value="{{formData.phone}}" bindinput="onInputChange" data-field="phone"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">地址</text>
          <input class="form-input" placeholder="请输入地址" value="{{formData.address}}" bindinput="onInputChange" data-field="address"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">邮箱</text>
          <input class="form-input" placeholder="请输入邮箱" value="{{formData.email}}" bindinput="onInputChange" data-field="email"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">网址</text>
          <input class="form-input" placeholder="请输入网址" value="{{formData.website}}" bindinput="onInputChange" data-field="website"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">微信</text>
          <input class="form-input" placeholder="请输入微信" value="{{formData.wechat}}" bindinput="onInputChange" data-field="wechat"/>
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">媒体资源</view>
        
        <view class="form-item">
          <text class="form-label">头像URL</text>
          <input class="form-input" placeholder="请输入头像URL" value="{{formData.avatar}}" bindinput="onInputChange" data-field="avatar"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">图片URL</text>
          <input class="form-input" placeholder="请输入图片URL" value="{{formData.images}}" bindinput="onInputChange" data-field="images"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">视频URL</text>
          <input class="form-input" placeholder="请输入视频URL" value="{{formData.videos}}" bindinput="onInputChange" data-field="videos"/>
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">业务信息</view>
        
        <view class="form-item">
          <text class="form-label">业务简介</text>
          <textarea class="form-textarea" placeholder="请输入业务简介" value="{{formData.businessIntro}}" bindinput="onInputChange" data-field="businessIntro"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">备注信息</text>
          <textarea class="form-textarea" placeholder="请输入备注信息" value="{{formData.remark}}" bindinput="onInputChange" data-field="remark"/>
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">设置</view>
        
        <view class="form-item">
          <text class="form-label">是否公开</text>
          <picker class="form-picker" range="{{publicOptions}}" value="{{formData.isPublic}}" bindchange="onPickerChange" data-field="isPublic">
            <view class="picker-text">{{publicOptions[formData.isPublic]}}</view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">审核状态</text>
          <picker class="form-picker" range="{{auditOptions}}" value="{{formData.auditStatus}}" bindchange="onPickerChange" data-field="auditStatus">
            <view class="picker-text">{{auditOptions[formData.auditStatus]}}</view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="form-label">名片类型</text>
          <input class="form-input" placeholder="请输入名片类型" value="{{formData.cardType}}" bindinput="onInputChange" data-field="cardType"/>
        </view>
      </view>
    </scroll-view>
  </view>

  <view class="footer-actions">
    <button class="btn-cancel" bindtap="onCancel">取消</button>
    <button class="btn-confirm" bindtap="onConfirm">确认添加</button>
  </view>
</view>