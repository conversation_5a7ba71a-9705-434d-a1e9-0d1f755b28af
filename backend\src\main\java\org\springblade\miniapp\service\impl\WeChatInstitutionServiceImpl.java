/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.miniapp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import org.springblade.business.institution.entity.Institution;
import org.springblade.business.institution.entity.InstitutionAuditLog;
import org.springblade.business.institution.entity.InstitutionType;
import org.springblade.business.institution.entity.UserInstitution;
import org.springblade.business.institution.mapper.InstitutionAuditLogMapper;
import org.springblade.business.institution.mapper.InstitutionMapper;
import org.springblade.business.institution.mapper.InstitutionTypeMapper;
import org.springblade.business.institution.mapper.UserInstitutionMapper;
import org.springblade.business.institution.vo.InstitutionVO;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.user.mapper.SupPostMapper;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.exception.SecureException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.miniapp.service.WeChatIInstitutionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 机构主表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */

@Service
public class WeChatInstitutionServiceImpl extends BaseServiceImpl<InstitutionMapper, Institution> implements WeChatIInstitutionService {

	@Resource
	private SupPostMapper supPostMapper;
	@Resource
	private UserInstitutionMapper userInstitutionMapper;
	@Resource
	private InstitutionTypeMapper institutionTypeMapper;
	@Resource
	private InstitutionAuditLogMapper institutionAuditLogMapper;
	@Resource
	private InstitutionMapper institutionMapper;



	@Override
	public IPage<InstitutionVO> selectInstitutionPage(IPage<InstitutionVO> page, Map<String, Object> params) {
		return page.setRecords(baseMapper.selectInstitutionPage(page, params));
	}

	@Override
	@Transactional
	public InstitutionVO getInstitutionDetailWithPosts(InstitutionVO institutionvo) {
		//1.查询机构详细
		if (institutionvo == null){
			throw new ServiceException("请选择机构");
		}
		//默认按照创建时间来排序，也支持用top置顶
		Institution institution = baseMapper.getInstutionById(institutionvo.getId());
		InstitutionVO institutionVO = new InstitutionVO();
		if (institution == null){
			throw new ServiceException("机构不存在");
		}
		BeanUtil.copyProperties(institution, institutionVO);
		Map<String, Object> params = new HashMap<>();
		params.put("status", 1); // 再 put 1
		params.put("createUser", AuthUtil.getUserId());
		params.put("visitUser", AuthUtil.getUserId());
		params.put("institutionId", institutionvo.getId());
		// 添加经纬度信息用于距离计算
		if (institutionvo.getLatitude() != null && institutionvo.getLongitude() != null) {
			params.put("latitude", institutionvo.getLatitude());
			params.put("longitude", institutionvo.getLongitude());
		}
		//2.查询机构关联的所有帖子
		List<SupPostVO> posts =supPostMapper.selectPostsByInstitutionId(params);
		if (posts == null){
			return institutionVO;
		}
		//查询机构名
		if (institutionVO.getTypeId() != null) {
			InstitutionType institutionType = institutionTypeMapper.selectById(institutionVO.getTypeId());
			institutionVO.setTypeName(institutionType.getName());
		}

		//查询审核信息日志
		if (institutionVO.getAuditStatus() != null && institutionVO.getStatus().equals("1")) {
			InstitutionAuditLog institutionAuditLog = institutionAuditLogMapper.selectById(institutionVO.getId());
			if (institutionAuditLog != null){
				institutionVO.setAuditLog(institutionAuditLog);
			}
		}
		//关联的帖子总数
		institutionVO.setPostCount(posts.size());
		//3，数据组合VO
		institutionVO.setPosts(posts);
		//4.记录一条浏览记录日志
		Boolean save = institutionMapper.saveViewLog(AuthUtil.getUserId(), institutionVO.getId());
		if (!save){
			throw new ServiceException("保存浏览记录失败");
		}

		return institutionVO;
	}

	@Override
	@Transactional
	public boolean applyInstitution(Institution institution) {
		//1.保存机构信息
		institution.setApplyTime(LocalDateTime.now());
		institution.setApplyUserId(AuthUtil.getUserId());
		boolean save = this.save(institution);
		if (!save){
			return false;
		}
		//2.记录用户机构关联表
		UserInstitution userInstitution = new UserInstitution();
		userInstitution.setInstitutionId(institution.getId());
		userInstitution.setUserId(AuthUtil.getUserId());
		userInstitution.setRole("owner");


		userInstitutionMapper.insert(userInstitution);
		return true;
	}

	@Override
	public IPage<InstitutionVO> getInstitutionPage(InstitutionVO institutionvo, Query query) {
		//判断是否未登录且页码大于5页
		Long userId = AuthUtil.getUserId();
		System.out.println("当前userId: " + userId);
		System.out.println("当前页码: " + query.getCurrent());
		if ((userId == null || userId == -1L) && query.getCurrent() > 5) {
			throw new SecureException("登录访问更多数据");
		}
		//没有登录不允许进行内容模糊查询
		String content = institutionvo.getDescription();
		// 只要有内容搜索，必须登录
		if (content != null && !content.trim().isEmpty() && (userId == null || userId == -1L)) {
			throw new SecureException("请先登录后再进行内容搜索");
		}
		Map<String, Object> params = new HashMap<>();
		params.put("visitUser",AuthUtil.getUserId());
		params.put("typeId", institutionvo.getTypeId());
		params.put("name", institutionvo.getName());
		params.put("description", institutionvo.getDescription());

		IPage<InstitutionVO> pages = this.selectInstitutionPage(Condition.getPage(query), params);
		//补充信息日志和机构名和关联的贴子数
		pages.getRecords().forEach(item -> {
						if (item.getAuditStatus() != null && item.getStatus().equals("1")) {
							InstitutionAuditLog institutionAuditLog = institutionAuditLogMapper.selectById(item.getId());
							item.setAuditLog(institutionAuditLog);
						}
						if (item.getTypeId() != null) {
							InstitutionType institutionType = institutionTypeMapper.selectById(item.getTypeId());
							item.setTypeName(institutionType.getName());
						}
						item.setPostCount(supPostMapper.getPostCountByInstitutionId(item.getId()));
		});

		return pages;
	}

	@Override
	public boolean updateInstitutionById(Institution institution) {
		//检查当前用户是不是·机机构创建者
		BladeUser user = AuthUtil.getUser();
		if (user == null){
			throw new SecureException("请登录");
		}
		Long userId = user.getUserId();
		//根据机构id查询机构创建者
		Institution institution1 = this.getById(institution.getId());
		if (institution1 == null){
			throw new ServiceException("机构不存在");
		}
		if (!institution1.getApplyUserId().equals(userId)){
			throw new SecureException("您没有权限修改该机构");
		}
		//设置不允许用户修改的机构字段和状态
		institution.setApplyUserId(null);
		institution.setStatus(null);
		institution.setCreateUser(null);
		institution.setCreateTime(null);
		institution.setUpdateUser(null);
		institution.setUpdateTime(null);
		institution.setIsDeleted(null);
		institution.setApplyTime(null);
		institution.setLastAuditTime(null);
		institution.setLastAuditUserId(null);
		institution.setAuditStatus(null);
		institution.setAuditRemark(null);
		institution.setApplyUserId(null);
		institution.setApplyTime(null);
		institution.setLastAuditTime(null);
		institution.setLastAuditUserId(null);
		institution.setUpdateUser(userId);
		return this.updateById(institution);
	}

	@Override
	public boolean openOrClose(String ids) {
		//检查当前用户是不是·机机构创建者
		BladeUser user = AuthUtil.getUser();
		if (user == null){
			throw new SecureException("请登录");
		}
		Long userId = user.getUserId();
		List<Institution> institutions = this.listByIds(Func.toLongList(ids));
		if (institutions == null || institutions.isEmpty()){
			throw new ServiceException("机构不存在");
		}
		for (Institution institution : institutions) {
			if (!institution.getApplyUserId().equals(userId)){
				throw new SecureException("您没有权限");
			}
		}
		//把查询出来的机构的状态改为禁用或者启动
		for (Institution institution : institutions) {
			//判断当前机构状态
			if (institution.getStatus() == 0){
				institution.setStatus(1);
			}
			else {
				institution.setStatus(0);
			}
		}
		return this.updateBatchById(institutions);
	}

	@Override
	public IPage<InstitutionVO> pageByUserId(IPage<Object> page) {
		if (AuthUtil.getUser() == null){
			throw new SecureException("请登录");
		}
		BladeUser user = AuthUtil.getUser();
		if (user != null){
			return this.baseMapper.pageByUserId(page, user.getUserId());
		}
		return null;
	}

    @Override
    public List<InstitutionType> getInstitutionTypes() {
		return institutionTypeMapper.selectList(Wrappers.<InstitutionType>lambdaQuery()
			.eq(InstitutionType::getStatus, 1)
			.orderByAsc(InstitutionType::getSortOrder));
	}

	@Override
	public boolean like(Long id) {
		//鉴权
		if (AuthUtil.getUser() == null){
			throw new SecureException("请登录");
		}
		//查询点赞记录表
		Integer likeCount = institutionMapper.getLikeCount(id, AuthUtil.getUserId());
		//判断用户是否已经点赞了
		//如果已经点赞提示用户已经点赞了
		if (likeCount > 0){
			throw new ServiceException("您已经点赞了");
		}
		//如果没有点赞就直接点赞
        return institutionMapper.saveLikeLog(id, AuthUtil.getUserId());
	}

	@Override
	public boolean favorite(Long id) {
		//鉴权
		if (AuthUtil.getUser() == null){
			throw new SecureException("请登录");
		}
		//查询收藏记录表
		Integer favoriteCount = institutionMapper.getFavoriteCount(id, AuthUtil.getUserId());
		//判断用户是否已经收藏了
		//如果已经点赞提示用户已经收藏了
		if (favoriteCount > 0){
			throw new ServiceException("您已经收藏了");
		}
		//如果没有收藏就直接收藏
        return institutionMapper.saveFavoriteLog(id, AuthUtil.getUserId());
	}

}
