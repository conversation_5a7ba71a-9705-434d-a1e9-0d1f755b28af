package org.springblade.miniapp.controller.open;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.service.IBusinessCardService;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat-open/card")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序个人名片接口")
public class WeChatBusinessCardOpenController {

	@Resource
	private final IBusinessCardService businessCardService;



	/**
	 * 获取当前用户名片
	 */
	@GetMapping("my-card")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "获取当前用户名片", description = "获取当前登录用户的名片信息")
	public R<BusinessCardVO> getMyCard() {
		Long userId = AuthUtil.getUserId();
		BusinessCard query = new BusinessCard();
		query.setCreateUser(userId);

		BusinessCard myCard = businessCardService.getOne(Condition.getQueryWrapper(query));
		if (myCard == null) {
			// 如果用户还没有名片，返回空对象
			return R.data(new BusinessCardVO());
		}

		BusinessCardVO businessCardVO = new BusinessCardVO();
		BeanUtil.copy(myCard, businessCardVO);
		return R.data(businessCardVO);
	}

	/**
	 * 名片详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "详情", description = "传入businessCard")
	public R<BusinessCardVO> detail(BusinessCardVO businessCard) {
		return R.data(businessCardService.getDetail(businessCard));
	}

	/**
	 * 自定义分页 名片信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入businessCard")
	public R<IPage<BusinessCardVO>> page(BusinessCardVO businessCard, Query query) {
		IPage<BusinessCardVO> pages = businessCardService.selectBusinessCardPage(Condition.getPage(query), businessCard);
		return R.data(pages);
	}
}
