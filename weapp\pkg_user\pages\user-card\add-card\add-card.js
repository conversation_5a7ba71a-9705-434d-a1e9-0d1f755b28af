// pkg_user/pages/add-card/add-card.js
const businessCardStore = require('../../../../stores/businessCardStore.js');

Page({
  data: {
    // 页面模式：create(创建) 或 edit(编辑)
    mode: 'create',
    cardId: null,

    // 表单数据
    formData: {
      fullName: '',
      company: '',
      jobTitle: '',
      businessProfile: '',
      phone: '',
      email: '',
      address: '',
      website: '',
      weixin: '',
      gender: 0, // 0:保密, 1:男, 2:女
      isPublic: 1, // 0:私密, 1:公开
      description: '',
      avatar: '',
      images: '',
      video: ''
    },

    // 性别选项
    genderOptions: [
      { value: 0, label: '保密' },
      { value: 1, label: '男' },
      { value: 2, label: '女' }
    ],

    // 公开状态选项
    publicOptions: [
      { value: 1, label: '公开' },
      { value: 0, label: '私密' }
    ],

    // 图片列表
    imageList: [],

    // 提交状态
    submitting: false,
    loading: false
  },

  onLoad(options) {
    console.log('名片编辑页面加载', options);

    const { id, mode } = options;

    this.setData({
      mode: mode || 'create',
      cardId: id || null
    });

    // 如果是编辑模式，加载现有数据
    if (mode === 'edit' && id) {
      this.loadCardData(id);
    }
  },

  /**
   * 加载名片数据（编辑模式）
   */
  async loadCardData(cardId) {
    this.setData({ loading: true });

    try {
      const result = await businessCardStore.getMyCard();

      if (result.success && result.data && result.data.id) {
        const cardData = result.data;

        // 处理图片列表
        const imageList = cardData.images ? cardData.images.split(',').filter(img => img.trim()) : [];

        this.setData({
          formData: {
            ...this.data.formData,
            ...cardData
          },
          imageList: imageList
        });
      } else {
        wx.showToast({
          title: '加载名片数据失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载名片数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 输入框变化
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 性别选择
   */
  onGenderChange(e) {
    const gender = parseInt(e.detail.value);
    this.setData({
      'formData.gender': gender
    });
  },

  /**
   * 公开状态选择
   */
  onPublicChange(e) {
    const isPublic = parseInt(e.detail.value);
    this.setData({
      'formData.isPublic': isPublic
    });
  },

  /**
   * 选择头像
   */
  onChooseAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        // TODO: 这里应该上传到服务器并获取URL
        this.setData({
          'formData.avatar': tempFilePath
        });
      },
      fail: (error) => {
        console.error('选择头像失败:', error);
        wx.showToast({
          title: '选择头像失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 选择图片
   */
  onChooseImages() {
    const currentCount = this.data.imageList.length;
    const maxCount = 6; // 最多6张图片

    if (currentCount >= maxCount) {
      wx.showToast({
        title: `最多只能选择${maxCount}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxCount - currentCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newImages = res.tempFilePaths;
        const updatedImageList = [...this.data.imageList, ...newImages];

        // TODO: 这里应该上传到服务器并获取URL
        this.setData({
          imageList: updatedImageList,
          'formData.images': updatedImageList.join(',')
        });
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 删除图片
   */
  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const imageList = [...this.data.imageList];

    imageList.splice(index, 1);

    this.setData({
      imageList: imageList,
      'formData.images': imageList.join(',')
    });
  },

  /**
   * 选择视频
   */
  onChooseVideo() {
    wx.chooseVideo({
      sourceType: ['album', 'camera'],
      maxDuration: 60, // 最长60秒
      camera: 'back',
      success: (res) => {
        // TODO: 这里应该上传到服务器并获取URL
        this.setData({
          'formData.video': res.tempFilePath
        });
      },
      fail: (error) => {
        console.error('选择视频失败:', error);
        wx.showToast({
          title: '选择视频失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 删除视频
   */
  onDeleteVideo() {
    this.setData({
      'formData.video': ''
    });
  },

  /**
   * 提交名片
   */
  async onSubmit() {
    if (this.data.submitting) return;

    // 表单验证
    const validation = businessCardStore.validateCard(this.data.formData);
    if (!validation.isValid) {
      wx.showToast({
        title: validation.errors[0],
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ submitting: true });

      // 准备提交数据
      const submitData = {
        ...this.data.formData
      };

      // 如果是编辑模式，添加ID
      if (this.data.mode === 'edit' && this.data.cardId) {
        submitData.id = this.data.cardId;
      }

      const result = await businessCardStore.saveCard(submitData);

      if (result.success) {
        wx.showToast({
          title: result.message || '保存成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: result.message || '保存失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('保存名片失败:', error);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 预览名片
   */
  onPreview() {
    // 使用Store验证
    const validation = businessCardStore.validateCard(this.data.formData);
    if (!validation.isValid) {
      wx.showToast({
        title: validation.errors[0],
        icon: 'none'
      });
      return;
    }

    // 格式化数据用于预览
    const previewData = businessCardStore.formatCardForDisplay(this.data.formData);
    const cardData = encodeURIComponent(JSON.stringify(previewData));

    wx.navigateTo({
      url: `/pkg_user/pages/card-detail/card-detail?data=${cardData}&preview=true`,
      success: () => {
        console.log('跳转到预览页面成功');
      },
      fail: (error) => {
        console.error('跳转失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 重置表单
   */
  onReset() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            formData: {
              fullName: '',
              company: '',
              jobTitle: '',
              businessProfile: '',
              phone: '',
              email: '',
              address: '',
              website: '',
              weixin: '',
              gender: 0,
              isPublic: 1,
              description: '',
              avatar: '',
              images: '',
              video: ''
            },
            imageList: []
          });

          wx.showToast({
            title: '已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 清理临时数据
    console.log('名片编辑页面卸载');
  }
});
