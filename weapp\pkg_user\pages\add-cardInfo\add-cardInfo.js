Page({
  data: {
    formData: {
      cardId: '',
      company: '',
      position: '',
      businessIntro: '',
      name: '',
      gender: 1,
      phone: '',
      address: '',
      email: '',
      website: '',
      wechat: '',
      avatar: '/assets/images/def-avatar.png',
      logo: '/assets/images/bot-avatar.png',
      qrCode: '/assets/images/bot-avatar.png',
      images: '',
      videos: '',
      remark: '',
      isPublic: 1,
      updateTime: '',
      auditStatus: 1,
      cardType: ''
    },
    genderOptions: ['保密', '男', '女'],
    publicOptions: ['否', '是'],
    auditOptions: ['待审核', '已通过', '未通过']
  },

  onLoad() {
    // 设置默认的更新时间
    this.setDefaultUpdateTime();
  },

  // 设置默认更新时间
  setDefaultUpdateTime() {
    const now = new Date();
    const updateTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    
    this.setData({
      'formData.updateTime': updateTime
    });
  },

  // 输入框变化处理
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择器变化处理
  onPickerChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = parseInt(e.detail.value);
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 取消操作
  onCancel() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消添加名片吗？已填写的内容将丢失。',
      confirmColor: '#ff8080',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 确认添加
  onConfirm() {
    const formData = this.data.formData;
    
    // 验证必填字段
    if (!formData.cardId.trim()) {
      wx.showToast({
        title: '请输入名片ID',
        icon: 'none'
      });
      return;
    }
    
    if (!formData.company.trim()) {
      wx.showToast({
        title: '请输入公司名称',
        icon: 'none'
      });
      return;
    }
    
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }
    
    // 处理数组字段
    const newCard = {
      ...formData,
      images: formData.images ? [formData.images] : [],
      videos: formData.videos ? [formData.videos] : []
    };
    
    // 获取页面栈
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2]; // 获取上一个页面
    
    if (prevPage && prevPage.onAddCard) {
      // 调用上一个页面的添加方法
      prevPage.onAddCard({ detail: newCard });
    }
    
    wx.showToast({
      title: '添加成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    });
  },

  // 页面显示时设置更新时间
  onShow() {
    this.setDefaultUpdateTime();
  }
}); 