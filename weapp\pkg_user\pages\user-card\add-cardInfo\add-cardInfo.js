// 引入名片管理 Store
const businessCardStore = require('../../../../stores/businessCardStore.js');

Page({
  data: {
    // 页面模式：create(创建) 或 edit(编辑)
    mode: 'create',
    cardId: null,
    loading: false,
    submitting: false,

    formData: {
      id: null, // 后端需要的ID字段
      company: '',
      jobTitle: '', // 对应后端的jobTitle字段
      businessProfile: '', // 对应后端的businessProfile字段
      fullName: '', // 对应后端的fullName字段
      gender: 0, // 0:保密, 1:男, 2:女
      phone: '',
      address: '',
      email: '',
      website: '',
      weixin: '', // 对应后端的weixin字段
      avatar: '',
      images: '',
      video: '',
      description: '', // 对应后端的description字段
      isPublic: 1 // 0:私密, 1:公开
    },
    genderOptions: ['保密', '男', '女'],
    publicOptions: ['私密', '公开']
  },

  onLoad(options) {
    console.log('页面参数:', options);

    // 检查是否是编辑模式
    if (options.mode === 'edit' && options.cardId) {
      this.setData({
        mode: 'edit',
        cardId: options.cardId
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '编辑名片'
      });

      // 加载名片数据
      this.loadCardData();
    } else {
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '添加名片'
      });
    }

    // 设置默认的更新时间
    this.setDefaultUpdateTime();
  },

  /**
   * 加载名片数据（编辑模式）
   */
  async loadCardData() {
    this.setData({ loading: true });

    try {
      const result = await businessCardStore.getMyCard();

      if (result.success && result.data && result.data.id) {
        const cardData = result.data;

        this.setData({
          formData: {
            id: cardData.id,
            company: cardData.company || '',
            jobTitle: cardData.jobTitle || '',
            businessProfile: cardData.businessProfile || '',
            fullName: cardData.fullName || '',
            gender: cardData.gender || 0,
            phone: cardData.phone || '',
            address: cardData.address || '',
            email: cardData.email || '',
            website: cardData.website || '',
            weixin: cardData.weixin || '',
            avatar: cardData.avatar || '',
            images: cardData.images || '',
            video: cardData.video || '',
            description: cardData.description || '',
            isPublic: cardData.isPublic || 1
          }
        });
      } else {
        wx.showToast({
          title: '加载名片数据失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载名片数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 设置默认更新时间
  setDefaultUpdateTime() {
    const now = new Date();
    const updateTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

    this.setData({
      'formData.updateTime': updateTime
    });
  },

  // 输入框变化处理
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择器变化处理
  onPickerChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = parseInt(e.detail.value);
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 取消操作
  onCancel() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消添加名片吗？已填写的内容将丢失。',
      confirmColor: '#ff8080',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 确认保存
  async onConfirm() {
    const formData = this.data.formData;

    // 验证必填字段
    if (!formData.fullName.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }

    if (!formData.phone.trim()) {
      wx.showToast({
        title: '请输入电话号码',
        icon: 'none'
      });
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    // 验证邮箱格式（如果填写了邮箱）
    if (formData.email && formData.email.trim() !== '') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        wx.showToast({
          title: '请输入正确的邮箱地址',
          icon: 'none'
        });
        return;
      }
    }

    this.setData({ submitting: true });

    try {
      // 显示加载状态
      wx.showLoading({
        title: this.data.mode === 'edit' ? '保存中...' : '创建中...'
      });

      // 调用保存API
      const result = await businessCardStore.saveCard(formData);

      wx.hideLoading();

      if (result.success) {
        wx.showToast({
          title: this.data.mode === 'edit' ? '保存成功' : '创建成功',
          icon: 'success',
          duration: 1500,
          success: () => {
            // 延迟返回，让用户看到成功提示
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          }
        });
      } else {
        wx.showToast({
          title: result.message || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存名片失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 页面显示时设置更新时间
  onShow() {
    this.setDefaultUpdateTime();
  }
}); 