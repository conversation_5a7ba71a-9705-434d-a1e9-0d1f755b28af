// const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址
const BASE_URL = 'http://192.168.31.103'; // 替换为您的后端API地址

const { handleLoginExpired } = require('./loginHandler');

// 请求拦截器
const requestInterceptor = (config) => {
  const token = wx.getStorageSync('token').value;
  const openId = wx.getStorageSync('openId');

  config.header = {
    ...config.header,
    'Tenant-Id' :"000000",
    'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,
  };

  if (token) {
    config.header = {
      ...config.header,
      'Blade-Auth': `Bearer ${token}`
    };
  }

  // 添加OpenID头
  if (openId) {
    config.header = {
      ...config.header,
      'X-Open-ID': openId
    };
  }
  
  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  const { data } = response;
  // 处理token过期
  if (data.code === 401) {
    handleLoginExpired();
    return Promise.reject(new Error('登录已过期，请重新登录'));
  }
  
  return data;
};

// 刷新token
const refreshToken = () => {
  return new Promise((resolve, reject) => {
    const refreshToken = wx.getStorageSync('refreshToken');
    if (!refreshToken) {
      reject(new Error('未找到刷新token'));
      return;
    }

    wx.request({
      url: `${BASE_URL}/blade-auth/token`,
      method: 'POST',
      data: {
        grantType: 'refresh_token',
        refreshToken: refreshToken
      },
      success: (res) => {
        if (res.data.success) {
          const { accessToken, refreshToken } = res.data.data;
          wx.setStorageSync('token', accessToken);
          wx.setStorageSync('refreshToken', refreshToken);
          resolve(accessToken);
        } else {
          reject(new Error(res.data.msg));
        }
      },
      fail: reject
    });
  });
};

// 统一请求方法
const request = (options) => {
  const config = requestInterceptor(options);
  const fullUrl = `${BASE_URL}${config.url}`;

  console.log('=== 发起网络请求 ===');
  console.log('请求URL:', fullUrl);
  console.log('请求方法:', config.method);
  console.log('请求头:', JSON.stringify(config.header, null, 2));
  console.log('请求数据:', JSON.stringify(config.data, null, 2));
  console.log('BASE_URL:', BASE_URL);

  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    wx.request({
      ...config,
      url: fullUrl,
      success: (res) => {
        const endTime = Date.now();
        console.log(`=== 网络请求成功 (耗时: ${endTime - startTime}ms) ===`);
        console.log('响应状态码:', res.statusCode);
        console.log('响应头:', JSON.stringify(res.header, null, 2));
        console.log('响应数据:', JSON.stringify(res.data, null, 2));

        try {
          const result = responseInterceptor(res);
          resolve(result);
        } catch (error) {
          console.error('响应拦截器处理失败:', error);
          reject(error);
        }
      },
      fail: (err) => {
        const endTime = Date.now();
        console.error(`=== 网络请求失败 (耗时: ${endTime - startTime}ms) ===`);
        console.error('请求URL:', fullUrl);
        console.error('错误信息:', JSON.stringify(err, null, 2));
        console.error('错误类型:', err.errMsg);

        // 增强错误信息
        const enhancedError = new Error(`网络请求失败: ${err.errMsg || '未知错误'}`);
        enhancedError.originalError = err;
        enhancedError.requestUrl = fullUrl;
        enhancedError.requestConfig = config;

        reject(enhancedError);
      }
    });
  });
};

module.exports = {
  request,
  refreshToken
}; 