<view class="user-detail-bg">
  <image class="header-bg" src="{{user.bgImage}}" mode="aspectFill"/>
  <view class="user-card">
    <image class="avatar" src="{{user.avatar}}" />
    <view class="user-main">
      <text class="nickname">{{user.nickname}}</text>
      <text class="ip">IP：{{user.ipLocation}}</text>
      <text class="msg">{{user.message}}</text>
    </view>
  </view>
  
  <view class="tab">
    <view class="tab-bar">
      <block wx:for="{{tabs}}" wx:key="index">
        <view
          class="tab-item {{activeTab === index ? 'active' : ''}}"
          data-index="{{index}}"
          bindtap="onTabChange"
        >
          {{item.name}}{{item.count !== undefined ? '(' + item.count + ')' : ''}}
          <view class="tab-underline" wx:if="{{activeTab === index}}"></view>
        </view>
      </block>
    </view>

    <!-- tab内容区 -->
    <!-- 动态 -->
    <view wx:if="{{activeTab === 0}}">
      <post-card
        wx:for="{{posts}}"
        wx:key="index"
        post="{{item}}"
      >
      </post-card>
      <!-- <block wx:for="{{posts}}" wx:key="id">
        <view class="post-item">
          <view class="post-content">{{item.content}}</view>
          <view class="post-img-list">
            <block wx:for="{{item.images}}" wx:key="index">
              <image 
                class="p-img" 
                src="{{item}}" 
                mode="aspectFill"
                wx:if="{{index < 3}}"/>
            </block>
          </view>
          <view class="post-footer">
            <view class="post-time">{{item.time}}</view>
            <view class="post-actions">
              <view class="action-item">
                <image class="action-icon" src="/assets/images/user-detail/icon/heart.png"></image>
                <text class="action-count">123</text>
              </view>
              <view class="action-item">
                <image class="action-icon" src="/assets/images/user-detail/icon/comment.png"></image>
                <text class="action-count">33</text>
              </view>
              <view class="action-item">
                <image class="action-icon" src="/assets/images/user-detail/icon/share.png"></image>
                <text class="action-count">8</text>
              </view>
            </view>
          </view>
        </view>
      </block> -->
    </view>
    <!-- 职位 -->
    <view wx:if="{{activeTab === 1}}">
      <job-card
        wx:for="{{jobs}}"
        wx:key="index"
        job="{{item}}"
      >
      </job-card>
    </view>
    <!-- 评论 -->
    <view wx:if="{{activeTab === 2}}">
      <comment-card
        wx:for="{{comments}}"
        wx:key="index"
        comment="{{item}}"
      >
      </comment-card>
    </view>
  </view>
  
</view> 