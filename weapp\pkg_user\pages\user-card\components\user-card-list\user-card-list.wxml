<view wx:if="{{!cardList || cardList.length === 0}}" class="no-cards">
  <text>暂无名片数据</text>
</view>
<view wx:else class="card-list-container">
  <view class="card-list-column">
    <block wx:for="{{cardList}}" wx:key="cardId">
      <view class="business-card">
        <!-- 卡片主体 -->
        <view class="card-content">
          <!-- 顶部：公司信息和头像 -->
          <view class="card-header">
            <view class="avatar-section">
              <image wx:if="{{item.avatar}}" class="avatar" src="{{item.avatar}}" mode="aspectFill"/>
              <view wx:else class="avatar-placeholder">
                <text class="avatar-text">{{item.name ? item.name.charAt(0) : '觅'}}</text>
              </view>
            </view>
            <view class="company-section">
              <text class="company-name">{{item.company}}</text>
              <view class="person-info">
                <text class="person-name">{{item.name}}</text>
                <text class="person-position">{{item.position}}</text>
                <view class="status-tag {{item.auditStatus === 1 ? 'approved' : item.auditStatus === 0 ? 'pending' : 'rejected'}}">
                  <text>{{item.auditStatus === 1 ? '已通过' : item.auditStatus === 0 ? '待审核' : '未通过'}}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 联系信息 -->
          <view class="contact-info">
            <view wx:if="{{item.address}}" class="info-item">
              <text class="info-icon">📍</text>
              <text class="info-text">{{item.address}}</text>
            </view>
            <view wx:if="{{item.phone}}" class="info-item">
              <text class="info-icon">📱</text>
              <text class="info-text">{{item.phone}}</text>
            </view>
            <view wx:if="{{item.wechat}}" class="info-item">
              <text class="info-icon">💬</text>
              <text class="info-text">{{item.wechat}}</text>
            </view>
            <view wx:if="{{item.businessIntro}}" class="info-item">
              <text class="info-text">{{item.businessIntro}}</text>
            </view>
          </view>

          <!-- 底部：时间、类型和操作按钮 -->
          <view class="card-footer">
            <view class="footer-left">
              <view class="time-info">
                <text class="time-icon">🕐</text>
                <text class="time-text">{{item.updateTime}}</text>
              </view>
              <view class="card-type-tag">
                <text>{{item.cardType}}</text>
              </view>
            </view>
            <view class="footer-right">
              <view class="action-buttons">
                <view class="action-btn" catchtap="onHideCard" data-index="{{index}}">
                  <text class="action-icon">👁️</text>
                </view>
                <view class="action-btn" catchtap="onEditCard" data-index="{{index}}">
                  <text class="action-icon">✏️</text>
                </view>
                <view class="action-btn" catchtap="onDeleteCard" data-index="{{index}}">
                  <text class="action-icon">🗑️</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 添加名片按钮 -->
    <view class="add-card-btn" bindtap="onAddCard">
      <view class="add-btn-content">
        <text class="add-icon">+</text>
        <text class="add-text">添加名片</text>
      </view>
    </view>
  </view>
</view>