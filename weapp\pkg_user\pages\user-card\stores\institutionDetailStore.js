/**
 * 机构详情Store
 * 负责机构详情页面的所有数据操作
 */

const { request } = require('../utils/request.js');
const { institution: API } = require('../config/api.js');

/**
 * 获取机构详情
 * @param {string} id 机构ID
 * @returns {Promise<Object>} 机构详情数据
 */
const getInstitutionDetail = async (id) => {
  try {
    if (!id) {
      throw new Error('机构ID不能为空');
    }

    const response = await request({
      url: `${API.detail}/${id}`,
      method: 'GET'
    });

    if (response.code === 200 && response.data) {
      return processInstitutionDetail(response.data);
    } else {
      throw new Error(response.msg || '获取机构详情失败');
    }
  } catch (error) {
    console.error('获取机构详情失败:', error);
    throw error;
  }
};

/**
 * 获取机构服务列表
 * @param {string} institutionId 机构ID
 * @returns {Promise<Array>} 服务列表
 */
const getInstitutionServices = async (institutionId) => {
  try {
    if (!institutionId) {
      throw new Error('机构ID不能为空');
    }

    const response = await request({
      url: `${API.services}/${institutionId}`,
      method: 'GET'
    });

    if (response.code === 200) {
      return processServiceList(response.data || []);
    } else {
      console.warn('获取服务列表失败:', response.msg);
      return [];
    }
  } catch (error) {
    console.error('获取机构服务列表失败:', error);
    return [];
  }
};

/**
 * 获取机构评价列表
 * @param {string} institutionId 机构ID
 * @param {Object} params 查询参数
 * @returns {Promise<Array>} 评价列表
 */
const getInstitutionReviews = async (institutionId, params = {}) => {
  try {
    if (!institutionId) {
      throw new Error('机构ID不能为空');
    }

    const response = await request({
      url: `${API.reviews}/${institutionId}`,
      method: 'GET',
      data: {
        current: params.current || 1,
        size: params.size || 10,
        ...params
      }
    });

    if (response.code === 200) {
      return processReviewList(response.data || []);
    } else {
      console.warn('获取评价列表失败:', response.msg);
      return [];
    }
  } catch (error) {
    console.error('获取机构评价列表失败:', error);
    return [];
  }
};

/**
 * 收藏/取消收藏机构
 * @param {string} institutionId 机构ID
 * @param {boolean} isFavorite 当前收藏状态
 * @returns {Promise<boolean>} 操作结果
 */
const toggleInstitutionFavorite = async (institutionId, isFavorite) => {
  try {
    if (!institutionId) {
      throw new Error('机构ID不能为空');
    }

    const response = await request({
      url: API.favorite,
      method: 'POST',
      data: {
        institutionId,
        action: isFavorite ? 'remove' : 'add'
      }
    });

    if (response.code === 200) {
      return !isFavorite; // 返回新的收藏状态
    } else {
      throw new Error(response.msg || '收藏操作失败');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    throw error;
  }
};

/**
 * 预约服务
 * @param {string} institutionId 机构ID
 * @param {string} serviceId 服务ID
 * @returns {Promise<boolean>} 预约结果
 */
const bookInstitutionService = async (institutionId, serviceId) => {
  try {
    if (!institutionId || !serviceId) {
      throw new Error('机构ID和服务ID不能为空');
    }

    const response = await request({
      url: API.book,
      method: 'POST',
      data: {
        institutionId,
        serviceId
      }
    });

    if (response.code === 200) {
      return true;
    } else {
      throw new Error(response.msg || '预约失败');
    }
  } catch (error) {
    console.error('预约服务失败:', error);
    throw error;
  }
};

/**
 * 记录机构浏览
 * @param {string} institutionId 机构ID
 * @returns {Promise<void>}
 */
const recordInstitutionView = async (institutionId) => {
  try {
    if (!institutionId) return;

    await request({
      url: API.view,
      method: 'POST',
      data: { institutionId }
    });
  } catch (error) {
    // 浏览记录失败不影响主要功能，只记录日志
    console.warn('记录浏览失败:', error);
  }
};

// ==================== 数据处理函数 ====================

/**
 * 处理机构详情数据
 * @param {Object} data 原始数据
 * @returns {Object} 处理后的数据
 */
const processInstitutionDetail = (data) => {
  return {
    id: data.id,
    name: data.name || '未知机构',
    logo: data.logo || '',
    description: data.description || '暂无描述',
    address: data.address || '地址未知',
    phone: data.phone || '',
    businessHours: data.businessHours || '营业时间未知',
    latitude: data.latitude || null,
    longitude: data.longitude || null,
    rating: data.rating || 0,
    reviewCount: data.reviewCount || 0,
    isFavorite: data.isFavorite || false,
    isLiked: data.isLiked || false,
    likeCount: data.likeCount || 0,
    viewCount: data.viewCount || 0,
    tags: data.tags || [],
    images: data.images || [],
    createTime: data.createTime || '',
    updateTime: data.updateTime || ''
  };
};

/**
 * 处理服务列表数据
 * @param {Array} data 原始数据
 * @returns {Array} 处理后的数据
 */
const processServiceList = (data) => {
  return data.map(item => ({
    id: item.id,
    name: item.name || '未知服务',
    description: item.description || '暂无描述',
    price: item.price || 0,
    duration: item.duration || '未知',
    isAvailable: item.isAvailable !== false,
    image: item.image || '',
    tags: item.tags || []
  }));
};

/**
 * 处理评价列表数据
 * @param {Array} data 原始数据
 * @returns {Array} 处理后的数据
 */
const processReviewList = (data) => {
  return data.map(item => ({
    id: item.id,
    userId: item.userId,
    userName: item.userName || '匿名用户',
    userAvatar: item.userAvatar || '',
    rating: item.rating || 0,
    content: item.content || '',
    images: item.images || [],
    createTime: item.createTime || '',
    isHelpful: item.isHelpful || false,
    helpfulCount: item.helpfulCount || 0
  }));
};

// ==================== 默认数据 ====================

/**
 * 获取默认机构详情数据
 * @returns {Object} 默认数据
 */
const getDefaultInstitutionDetail = () => {
  return {
    id: '',
    name: '机构信息加载中...',
    logo: '',
    description: '',
    address: '',
    phone: '',
    businessHours: '',
    latitude: null,
    longitude: null,
    rating: 0,
    reviewCount: 0,
    isFavorite: false,
    isLiked: false,
    likeCount: 0,
    viewCount: 0,
    tags: [],
    images: [],
    createTime: '',
    updateTime: ''
  };
};

// ==================== 导出 ====================

module.exports = {
  // 主要功能函数
  getInstitutionDetail,
  getInstitutionServices,
  getInstitutionReviews,
  toggleInstitutionFavorite,
  bookInstitutionService,
  recordInstitutionView,
  
  // 工具函数
  processInstitutionDetail,
  processServiceList,
  processReviewList,
  getDefaultInstitutionDetail
};
