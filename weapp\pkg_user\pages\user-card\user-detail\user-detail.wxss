.user-detail-bg {
  background: #f6f8fa;
  min-height: 100vh;
  position: relative;
}


/* 背景图 */
.header-bg {
  width: 100vw;
  height: 300rpx;
  object-fit: cover;
  /* border-radius: 0 0 32rpx 32rpx; */
  position: absolute;
  left: 0; top: -180rpx;
  z-index: 0;
}


/* 个人卡片 */
.user-card {
  display: flex;
  position: relative;
  background: #fff;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  align-items: center;
  margin: 180rpx 24rpx 0 24rpx;
  padding: 32rpx 26rpx;
  border-radius: 24rpx;
  z-index: 1;
}
.avatar {
  width: 140rpx; height: 140rpx; border-radius: 50%; margin-right: 24rpx;
  background: #eee;
}
.user-main {
  display: flex; flex-direction: column; flex: 1;
}
.nickname {
  font-size: 36rpx; font-weight: bold; color: #222;
}
.ip {
  font-size: 24rpx; color: #888; margin-top: 8rpx;
}
.msg {
  font-size: 26rpx; color: #666; margin-top: 8rpx;
}


/* tab栏 */
.tab-bar {
  display: flex;
  margin-bottom: 24rpx;
}
.tab-item {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  color: #999;
  padding: 24rpx 0 12rpx 0;
  position: relative;
  font-weight: 500;
}
.tab-item.active {
  color: rgb(255, 107, 107);
  font-weight: bold;
}
.tab-underline {
  position: absolute;
  left: 50%;
  bottom: 6rpx;
  transform: translateX(-50%);
  width: 60%;
  height: 6rpx;
  background: rgb(255, 107, 107);
  border-radius: 6rpx;
}

/* 动态内容样式 */
.post-item {
  background: #fff;
  margin: 0 24rpx 24rpx;
  padding: 26rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.post-content {
  font-size: 30rpx;
  color: #222;
  margin-bottom: 16rpx;
}
.post-img-list {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.p-img {
  /* object-fit: cover; */
  width: 200rpx;
  height: 200rpx;
}

/* 底部信息栏 */
.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.post-time {
  font-size: 24rpx;
  color: #888;
}
.post-actions {
  display: flex;
  align-items: center;
}
.action-item {
  display: flex;
  align-items: center;
  margin-left: 28rpx;
}
.action-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}
.action-count {
  font-size: 24rpx;
  color: #888;
}




/* 职位内容样式 */
.job-card {
  background: #fff;
  margin: 0 24rpx 24rpx;
  padding: 26rpx;
  border-radius: 24rpx;
}
.job-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
}
.job-tags {
  margin: 8rpx 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.tag {
  background: rgba(255, 107, 107, 0.08);
  color: rgb(255, 107, 107);
  border-radius: 8rpx;
  font-size: 24rpx;
  padding: 4rpx 14rpx;
  margin-right: 12rpx;
}
.salary {
  color: rgb(255, 107, 107);
  font-size: 26rpx;
  font-weight: bold;
  margin-left: auto;
}
.job-meta {
  display: flex;
  justify-content: space-between;
  color: #888;
  font-size: 24rpx;
}

/* 评论内容样式 */
.comment-item {
  background: #fff;
  margin: 0 24rpx 24rpx;
  padding: 26rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}
.com-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.com-user-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.com-user-info .username {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.com-user-info .time {
  font-size: 24rpx;
  color: #999;
}
.comment-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}