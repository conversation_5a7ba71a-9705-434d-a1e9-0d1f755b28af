<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.institution.mapper.InstitutionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="institutionResultMap" type="org.springblade.business.institution.vo.InstitutionVO">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="name" property="name"/>
        <result column="logo" property="logo"/>
        <result column="type_id" property="typeId"/>
        <result column="years_in_business" property="yearsInBusiness"/>
        <result column="description" property="description"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="phone" property="phone"/>
        <result column="backup_phone" property="backupPhone"/>
        <result column="email" property="email"/>
        <result column="wechat" property="wechat"/>
        <result column="qq" property="qq"/>
        <result column="license_no" property="licenseNo"/>
        <result column="license_image" property="licenseImage"/>
        <result column="legal_person" property="legalPerson"/>
        <result column="industry_license" property="industryLicense"/>
        <result column="tax_certificate" property="taxCertificate"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="district" property="district"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="is_store" property="isStore"/>
        <result column="service_radius" property="serviceRadius"/>
        <result column="has_delivery" property="hasDelivery"/>
        <result column="payment_methods" property="paymentMethods"/>
        <result column="special_services" property="specialServices"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="salt" property="salt"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="last_login_ip" property="lastLoginIp"/>
        <result column="is_locked" property="isLocked"/>
        <result column="business_hours" property="businessHours"/>
        <result column="images" property="images"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_remark" property="auditRemark"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="apply_time" property="applyTime"/>
        <result column="last_audit_time" property="lastAuditTime"/>
        <result column="last_audit_user_id" property="lastAuditUserId"/>
        <result column="top" property="top"/>
        <result column="type_name" property="typeName"/>

        <!-- 机构状态统计信息 -->
        <association property="institutionStats" javaType="org.springblade.business.institution.dto.InstitutionStatsDTO">
            <result column="is_liked" property="isLiked"/>
            <result column="is_favorite" property="isFavorite"/>
            <result column="is_view" property="isView"/>
            <result column="view_count" property="viewCount"/>
            <result column="like_count" property="likeCount"/>
            <result column="favorite_count" property="favoriteCount"/>
        </association>

    </resultMap>

<!--    封装sql复用-->
    <sql id="institution_info_state_select">
        CASE
            WHEN EXISTS (
                SELECT 1
                FROM urb_like
                WHERE relevancy_id = i.id
                AND type = '2'
                  AND user_id = #{model.visitUser}
        AND is_deleted = 0
        )
        THEN 1
        ELSE 0
        END AS is_liked,

        CASE
            WHEN EXISTS (
                SELECT 1
                FROM urb_favorite
                WHERE relevancy_id = i.id
                AND type = '2'
                  AND user_id = #{model.visitUser}
        AND is_deleted = 0
        )
        THEN 1
        ELSE 0
        END AS is_favorite,

        CASE
            WHEN EXISTS (
                SELECT 1
                FROM urb_view_log
                WHERE relevancy_id = i.id
                AND type = '2'
                  AND user_id = #{model.visitUser}
        AND is_deleted = 0
        )
        THEN 1
        ELSE 0
        END AS is_view
    </sql>


    <sql id="institution_info_join">
        LEFT JOIN urb_user u ON i.create_user = u.id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as view_count
            FROM urb_view_log
            where type = '2'
            and is_deleted = 0
            GROUP BY relevancy_id
        ) v ON i.id = v.relevancy_id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as like_count
            FROM urb_like
            where is_deleted = 0
            AND type = '2'
            GROUP BY relevancy_id
        ) l ON i.id = l.relevancy_id
        LEFT JOIN (
            SELECT relevancy_id, COUNT(1) as favorite_count
            FROM urb_favorite
            where is_deleted = 0
            AND type = '2'
            GROUP BY relevancy_id
        ) f ON i.id = f.relevancy_id
        LEFT JOIN urb_institution_type t ON i.type_id = t.id
    </sql>

    <sql id="institution_info_select">
        u.nickname,
        u.avatar,
        COALESCE(v.view_count, 0) as view_count,
        COALESCE(l.like_count, 0) as like_count,
        COALESCE(f.favorite_count, 0) as favorite_count,
        t.name as type_name
    </sql>


    <insert id="saveLikeLog">
            insert into urb_like(relevancy_id,user_id,type) values(#{id},#{userId},'2')
    </insert>
    <insert id="saveFavoriteLog">
        insert into urb_favorite(relevancy_id,user_id,type) values(#{id},#{userId},'2')
    </insert>
    <insert id="saveViewLog">
            insert into urb_view_log(relevancy_id,user_id,type) values(#{id},#{userId},'2')
    </insert>

<!--    TODO 连表点赞，收藏，浏览记录表来返回机构状态统计信息-->
    <select id="selectInstitutionPage" resultMap="institutionResultMap">
        select i.* ,
        <include refid="institution_info_select"/>
        <if test="model.visitUser != null">
            ,<include refid="institution_info_state_select"/>
        </if>
        from urb_institution as i
        <include refid="institution_info_join"/>
        <where>
            i.is_deleted = 0
            <if test="model.typeId != null">
                and i.type_id = #{model.typeId}
            </if>
            <if test="model.name != null and model.name != ''">
                and i.name like concat('%',#{model.name},'%')
            </if>
            <if test="model.description != null and model.description != ''">
                and i.description like concat('%',#{model.description},'%')
            </if>
        </where>
        ORDER BY
        CASE WHEN i.top = '1' THEN '0' ELSE '1' END,
        i.create_time DESC
    </select>
    <select id="pageByUserId" resultType="org.springblade.business.institution.vo.InstitutionVO">
            select * from urb_institution where is_deleted = 0
            and id in (select institution_id from urb_user_institution where user_id=#{userId})
    </select>
    <select id="getInstutionById" resultType="org.springblade.business.institution.entity.Institution">
            select * from urb_institution where id = #{id}
            and is_deleted = 0
            ORDER BY
                CASE WHEN top = '1' THEN '0' ELSE '1' END,
                create_time DESC

    </select>
    <select id="getLikeCount" resultType="java.lang.Integer">
        select count(1) from urb_like where relevancy_id = #{id}
        AND user_id = #{userId}
        AND type = '2'
    </select>
    <select id="getFavoriteCount" resultType="java.lang.Integer">
        select count(1) from urb_like where relevancy_id = #{id}
        AND user_id = #{userId}
        AND type = '2'
    </select>

</mapper>
