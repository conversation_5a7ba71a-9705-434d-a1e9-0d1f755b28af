package org.springblade.miniapp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据操作结果 DTO
 * 
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据操作结果")
public class DataOperateResultDTO {

    @Schema(description = "操作是否成功")
    private Boolean success;

    @Schema(description = "操作后的状态（点赞/收藏等）")
    private Boolean currentState;

    @Schema(description = "操作类型")
    private String action;

    @Schema(description = "操作消息")
    private String message;

    @Schema(description = "目标ID")
    private Long targetId;

    @Schema(description = "操作类型")
    private String type;

    /**
     * 创建点赞操作结果
     */
    public static DataOperateResultDTO createLikeResult(Long targetId, String type, boolean isLiked) {
        return DataOperateResultDTO.builder()
                .success(true)
                .currentState(isLiked)
                .action(isLiked ? "like" : "unlike")
                .message(isLiked ? "点赞成功" : "取消点赞成功")
                .targetId(targetId)
                .type(type)
                .build();
    }

    /**
     * 创建收藏操作结果
     */
    public static DataOperateResultDTO createFavoriteResult(Long targetId, String type, boolean isFavorited) {
        return DataOperateResultDTO.builder()
                .success(true)
                .currentState(isFavorited)
                .action(isFavorited ? "favorite" : "unfavorite")
                .message(isFavorited ? "收藏成功" : "取消收藏成功")
                .targetId(targetId)
                .type(type)
                .build();
    }

    /**
     * 创建分享操作结果
     */
    public static DataOperateResultDTO createShareResult(Long targetId, String type, boolean success) {
        return DataOperateResultDTO.builder()
                .success(success)
                .currentState(null)
                .action("share")
                .message(success ? "分享记录成功" : "分享记录失败")
                .targetId(targetId)
                .type(type)
                .build();
    }

    /**
     * 创建浏览操作结果
     */
    public static DataOperateResultDTO createViewResult(Long targetId, String type, boolean success) {
        return DataOperateResultDTO.builder()
                .success(success)
                .currentState(null)
                .action("view")
                .message(success ? "浏览记录成功" : "浏览记录失败")
                .targetId(targetId)
                .type(type)
                .build();
    }
}
