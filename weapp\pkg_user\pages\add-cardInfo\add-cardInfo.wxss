/* 添加名片页面样式 */
.add-card-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.form-container {
  flex: 1;
  padding: 24rpx;
}

.form-scroll {
  height: calc(100vh - 200rpx);
}

.form-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 600;
  line-height: 1.4;
}

.form-input {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  line-height: 1.4;
  min-height: 48rpx;
  -webkit-appearance: none;
  appearance: none;
}

.form-input:focus {
  border-color: #ff8080;
  box-shadow: 0 0 0 4rpx rgba(255, 128, 128, 0.1);
  outline: none;
}

.form-input::placeholder {
  color: #999;
  font-size: 28rpx;
}

.form-textarea {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  min-height: 140rpx;
  resize: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  line-height: 1.5;
  -webkit-appearance: none;
  appearance: none;
}

.form-textarea:focus {
  border-color: #ff8080;
  box-shadow: 0 0 0 4rpx rgba(255, 128, 128, 0.1);
  outline: none;
}

.form-textarea::placeholder {
  color: #999;
  font-size: 28rpx;
}

.form-picker {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  background: #fff;
  box-sizing: border-box;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  min-height: 48rpx;
  -webkit-appearance: none;
  appearance: none;
}

.form-picker:active {
  border-color: #ff8080;
  box-shadow: 0 0 0 4rpx rgba(255, 128, 128, 0.1);
}

.picker-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  min-height: 24rpx;
  display: block;
  width: 100%;
}

.picker-text:empty::before {
  content: '请选择';
  color: #999;
  font-size: 28rpx;
}

.footer-actions {
  padding: 32rpx 24rpx 40rpx 24rpx;
  display: flex;
  gap: 20rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.btn-cancel, .btn-confirm {
  flex: 1;
  padding: 28rpx 24rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  line-height: 1.4;
  min-height: 48rpx;
}

.btn-cancel {
  background: #f8f8f8;
  color: #666;
  border: 2rpx solid #e8e8e8;
}

.btn-cancel:active {
  background: #f0f0f0;
  transform: scale(0.98);
}

.btn-confirm {
  background: #ff8080;
  color: #fff;
  border: 2rpx solid #ff8080;
}

.btn-confirm:active {
  background: #ff6b6b;
  transform: scale(0.98);
} 