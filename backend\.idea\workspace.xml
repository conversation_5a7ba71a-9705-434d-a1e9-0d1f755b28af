<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fe531419-7b17-40bf-9621-b5563a72914a" name="更改" comment="fixed: 结构小程序端调整">
      <change beforePath="$PROJECT_DIR$/src/main/java/org/springblade/miniapp/controller/WeChatBusinessCardController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/springblade/miniapp/controller/WeChatBusinessCardController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/app.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/app.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/config/api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/config/api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/pages/local/local.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/pages/local/local.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/pkg_common/utils/request.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/pkg_common/utils/request.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/pkg_merchant/config/api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/pkg_merchant/config/api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/pkg_merchant/stores/merchantStore.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/pkg_merchant/stores/merchantStore.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/pkg_merchant/utils/request.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/pkg_merchant/utils/request.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/pkg_user/pages/user-card/user-card.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/pkg_user/pages/user-card/user-card.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/pkg_user/utils/request.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/pkg_user/utils/request.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/stores/businessCardStore.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/stores/businessCardStore.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../weapp/utils/request.js" beforeDir="false" afterPath="$PROJECT_DIR$/../weapp/utils/request.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="AnnotationType" />
        <option value="Enum" />
        <option value="Class" />
        <option value="Interface" />
        <option value="Mybatis Mapper" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PUSH_AUTO_UPDATE" value="true" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/.." value="prod" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="prod" />
                    <option name="lastUsedInstant" value="1753888827" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/.." />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../../manage/apache-maven-3.8.5/repository/org/springblade/blade-starter-mybatis/4.4.2/blade-starter-mybatis-4.4.2.jar!/org/springblade/core/mp/base/BaseEntity.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\Development\manage\apache-maven-3.8.5\repository" />
        <option name="mavenHome" value="$PROJECT_DIR$/../../../../manage/apache-maven-3.8.5" />
        <option name="userSettingsFile" value="D:\Development\manage\apache-maven-3.8.5\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yrtc44rSK73SRyMVjWv4ezaakw" />
  <component name="ProjectLevelVcsManager">
    <OptionsSetting value="false" id="Update" />
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;prod&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Development/project/self/easy-find-easy-post/backend/src/main/java/org/springblade/miniapp/mapper&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;8fcda207e9634010ec5ffaf43c304d97&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\Development\\ide\\ideaIU\\plugins\\javascript-impl\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="org.springblade.miniapp.service.impl" />
      <recent name="org.springblade.miniapp.service" />
      <recent name="org.springblade.business.points.service.impl" />
      <recent name="org.springblade.common.anno" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Development\project\self\easy-find-easy-post\backend\src\main\java\org\springblade\business" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Development\project\self\easy-find-easy-post\backend\src\main\java\org\springblade\business\points\mapper" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="org.springblade.miniapp.controller" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.Application">
    <configuration name="Application | #1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/Application.http" requestIdentifier="#1" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="EasyPostEasyFindBackend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.springblade.Application" />
      <option name="VM_PARAMETERS" value="--add-opens=java.base/java.lang=ALL-UNNAMED" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="HTTP Request.Application | #1" />
      <item itemvalue="Spring Boot.Application" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP Request.Application | #1" />
        <item itemvalue="HTTP Request.Application | #1" />
        <item itemvalue="HTTP Request.Application | #1" />
        <item itemvalue="HTTP Request.Application | #1" />
        <item itemvalue="HTTP Request.Application | #1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fe531419-7b17-40bf-9621-b5563a72914a" name="更改" comment="" />
      <created>1737906179516</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1737906179516</updated>
      <workItem from="1750604602334" duration="2461000" />
      <workItem from="1750765600946" duration="8369000" />
      <workItem from="1750875807772" duration="12137000" />
      <workItem from="1750935414935" duration="1537000" />
      <workItem from="1751015054279" duration="656000" />
      <workItem from="1751357090910" duration="1920000" />
      <workItem from="1751375473773" duration="690000" />
      <workItem from="1751423175389" duration="1830000" />
      <workItem from="1751439114478" duration="873000" />
      <workItem from="1751441670872" duration="3038000" />
      <workItem from="1751513833673" duration="2052000" />
      <workItem from="1751523523292" duration="2876000" />
      <workItem from="1751529186467" duration="10143000" />
      <workItem from="1751600075610" duration="210000" />
      <workItem from="1751600964548" duration="250000" />
      <workItem from="1751601597951" duration="591000" />
      <workItem from="1751602202046" duration="9226000" />
      <workItem from="1751623565780" duration="21000" />
      <workItem from="1751630708306" duration="112000" />
      <workItem from="1751796737973" duration="758000" />
      <workItem from="1751858218613" duration="19047000" />
      <workItem from="1751941609294" duration="6487000" />
      <workItem from="1752028954278" duration="26310000" />
      <workItem from="1752113881479" duration="17580000" />
      <workItem from="1752221227038" duration="1151000" />
      <workItem from="1752320098356" duration="12000" />
      <workItem from="1752412698975" duration="640000" />
      <workItem from="1752459739537" duration="12000" />
      <workItem from="1752737175687" duration="6276000" />
      <workItem from="1752749368487" duration="100000" />
      <workItem from="1752805078459" duration="12509000" />
      <workItem from="1752919805934" duration="4309000" />
      <workItem from="1752992606922" duration="2824000" />
      <workItem from="1752996541245" duration="3608000" />
      <workItem from="1753065624534" duration="2897000" />
      <workItem from="1753152255815" duration="4386000" />
      <workItem from="1753171661630" duration="1765000" />
      <workItem from="1753282750670" duration="4905000" />
      <workItem from="1753322963358" duration="6177000" />
      <workItem from="1753334814575" duration="23836000" />
      <workItem from="1753410774554" duration="26457000" />
      <workItem from="1753512914102" duration="18405000" />
      <workItem from="1753598124470" duration="8373000" />
      <workItem from="1753668724069" duration="1875000" />
      <workItem from="1753706436833" duration="1225000" />
      <workItem from="1753708088757" duration="2394000" />
      <workItem from="1753759508889" duration="19641000" />
      <workItem from="1753838748493" duration="1213000" />
      <workItem from="1753859821336" duration="6484000" />
      <workItem from="1753870420094" duration="462000" />
      <workItem from="1753871689372" duration="10165000" />
      <workItem from="1753884376119" duration="4241000" />
      <workItem from="1753924734942" duration="3151000" />
      <workItem from="1753931592153" duration="7473000" />
      <workItem from="1753948291835" duration="2968000" />
    </task>
    <task id="LOCAL-00001" summary="fixed 修复部分报错">
      <option name="closed" value="true" />
      <created>1751868914814</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751868914814</updated>
    </task>
    <task id="LOCAL-00002" summary="fixed: 修复合并报错">
      <option name="closed" value="true" />
      <created>1751873313423</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751873313423</updated>
    </task>
    <task id="LOCAL-00003" summary="fixed: 签到功能小程序页面添加">
      <option name="closed" value="true" />
      <created>1752046469079</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752046469079</updated>
    </task>
    <task id="LOCAL-00004" summary="fixed: 签到功能小程序页面添加">
      <option name="closed" value="true" />
      <created>1752049204940</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752049204940</updated>
    </task>
    <task id="LOCAL-00005" summary="fixed: 签到功能小程序页面添加">
      <option name="closed" value="true" />
      <created>1752049720310</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752049720310</updated>
    </task>
    <task id="LOCAL-00006" summary="fixed: 签到功能小程序页面添加">
      <option name="closed" value="true" />
      <created>1752061863609</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752061863609</updated>
    </task>
    <task id="LOCAL-00007" summary="fixed: 修复提交帖子空指针错误">
      <option name="closed" value="true" />
      <created>1753457022157</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753457022157</updated>
    </task>
    <task id="LOCAL-00008" summary="fixed: 修复举报查询失败问题">
      <option name="closed" value="true" />
      <created>1753514091067</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753514091067</updated>
    </task>
    <task id="LOCAL-00009" summary="fixed: 修复举报查询失败问题">
      <option name="closed" value="true" />
      <created>1753795208534</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753795208534</updated>
    </task>
    <task id="LOCAL-00010" summary="fixed: 结构小程序端调整">
      <option name="closed" value="true" />
      <created>1753891440553</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753891440553</updated>
    </task>
    <option name="localTasksCounter" value="11" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="prod" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="更改" />
    <MESSAGE value="fixed 修复部分报错" />
    <MESSAGE value="fixed: 修复合并报错" />
    <MESSAGE value="fixed: 修复提交帖子空指针错误" />
    <MESSAGE value="fixed: 签到功能小程序页面添加" />
    <MESSAGE value="fixed: 修复举报查询失败问题" />
    <MESSAGE value="fixed: 结构小程序端调整" />
    <option name="LAST_COMMIT_MESSAGE" value="fixed: 结构小程序端调整" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/org/springblade/miniapp/service/impl/WechatGroupServiceImpl.java</url>
          <line>34</line>
          <properties class="org.springblade.miniapp.service.impl.WechatGroupServiceImpl" method="selectGroupCategoryPage">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>