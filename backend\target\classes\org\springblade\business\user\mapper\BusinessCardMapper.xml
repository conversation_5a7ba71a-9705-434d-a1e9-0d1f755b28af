<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.BusinessCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessCardResultMap" type="org.springblade.business.user.vo.BusinessCardVO">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="company" property="company"/>
        <result column="job_title" property="jobTitle"/>
        <result column="business_profile" property="businessProfile"/>
        <result column="full_name" property="fullName"/>
        <result column="gender" property="gender"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="email" property="email"/>
        <result column="website" property="website"/>
        <result column="weixin" property="weixin"/>
        <result column="avatar" property="avatar"/>
        <result column="images" property="images"/>
        <result column="video" property="video"/>
        <result column="description" property="description"/>
        <result column="is_public" property="isPublic"/>
        <result column="nickname" property="app_nickname"/>
        <result column="mobile" property="app_mobile"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="view_count" property="viewCount"/>

        <!-- 名片状态统计信息 -->
        <association property="stats" javaType="org.springblade.business.user.dto.cardStatsDTO">
            <result column="is_liked" property="isLiked"/>
            <result column="is_favorite" property="isFavorite"/>
            <result column="view_count" property="viewCount"/>
            <result column="like_count" property="likeCount"/>
            <result column="favorite_count" property="favoriteCount"/>
        </association>
    </resultMap>


    <select id="selectBusinessCardPage" resultMap="businessCardResultMap">
        select ubs.*, uu.nickname, uu.mobile,
        COALESCE(v.view_count, 0) as view_count,
        COALESCE(l.like_count, 0) as like_count,
        COALESCE(f.favorite_count, 0) as favorite_count
        from urb_business_card ubs
        LEFT JOIN urb_user uu on ubs.create_user = uu.id
        LEFT JOIN (
        SELECT relevancy_id, COUNT(1) as view_count
        FROM urb_view_log
        where type = '0'
        and is_deleted = 0
        GROUP BY relevancy_id
        ) v ON ubs.id = v.relevancy_id
        LEFT JOIN (
        SELECT relevancy_id, COUNT(1) as like_count
        FROM urb_like
        where is_deleted = 0
        AND type = '0'
        GROUP BY relevancy_id
        ) l ON ubs.id = l.relevancy_id
        LEFT JOIN (
        SELECT relevancy_id, COUNT(1) as favorite_count
        FROM urb_favorite
        where is_deleted = 0
        AND type = '0'
        GROUP BY relevancy_id
        ) f ON ubs.id = f.relevancy_id
                 where ubs.is_deleted = 0
                 <if test="businessCard.auditStatus!=null">
                     and ubs.audit_status = #{businessCard.auditStatus}
                 </if>
                 <if test="businessCard.company!=null">
                     and ubs.company like concat('%',#{businessCard.company},'%')
                 </if>
                 <if test="businessCard.fullName!=null">
                     and ubs.full_name like concat('%',#{businessCard.fullName},'%')
                 </if>
                  <if test="businessCard.jobTitle!=null">
                      and ubs.job_title like concat('%',#{businessCard.jobTitle},'%')
                  </if>
                 <if test="businessCard.id!=null">
                     and ubs.id = #{businessCard.id}admin
                 </if>
        <!-- 距离范围过滤 -->
        <if test="Business.latitude != null and Business.longitude != null and Business.scope != null">
            AND (
            6371 * acos(
            cos(radians(#{Business.latitude}))
            * cos(radians(p.latitude))
            * cos(radians(p.longitude) - radians(#{Business.longitude}))
            + sin(radians(#{Business.latitude})) * sin(radians(p.latitude))
            )
            ) &lt;= #{Business.scope}
        </if>

        order by ubs.create_time desc
    </select>
    <select id="selectCardList" resultType="org.springblade.business.user.vo.BusinessCardVO">
        select ubs.*, uu.nickname, uu.mobile,
        COALESCE(v.view_count, 0) as view_count,
        COALESCE(l.like_count, 0) as like_count,
        COALESCE(f.favorite_count, 0) as favorite_count
        from urb_business_card ubs
        LEFT JOIN urb_user uu on ubs.create_user = uu.id
        LEFT JOIN (
        SELECT relevancy_id, COUNT(1) as view_count
        FROM urb_view_log
        where type = '0'
        and is_deleted = 0
        GROUP BY relevancy_id
        ) v ON ubs.id = v.relevancy_id
        LEFT JOIN (
        SELECT relevancy_id, COUNT(1) as like_count
        FROM urb_like
        where is_deleted = 0
        AND type = '0'
        GROUP BY relevancy_id
        ) l ON ubs.id = l.relevancy_id
        LEFT JOIN (
        SELECT relevancy_id, COUNT(1) as favorite_count
        FROM urb_favorite
        where is_deleted = 0
        AND type = '0'
        GROUP BY relevancy_id
        ) f ON ubs.id = f.relevancy_id
        where ubs.is_deleted = 0
        <if test="params.auditStatus!=null">
            and ubs.audit_status = #{businessCard.auditStatus}
        </if>
        <if test="businessCard.company!=null">
            and ubs.company like concat('%',#{businessCard.company},'%')
        </if>
        <if test="businessCard.fullName!=null">
            and ubs.full_name like concat('%',#{businessCard.fullName},'%')
        </if>
        <if test="businessCard.jobTitle!=null">
            and ubs.job_title like concat('%',#{businessCard.jobTitle},'%')
        </if>
        <if test="businessCard.id!=null">
            and ubs.id = #{businessCard.id}admin
        </if>
        <!-- 距离范围过滤 -->
        <if test="Business.latitude != null and Business.longitude != null and Business.scope != null">
            AND (
            6371 * acos(
            cos(radians(#{Business.latitude}))
            * cos(radians(p.latitude))
            * cos(radians(p.longitude) - radians(#{Business.longitude}))
            + sin(radians(#{Business.latitude})) * sin(radians(p.latitude))
            )
            ) &lt;= #{Business.scope}
        </if>
    </select>
</mapper>
