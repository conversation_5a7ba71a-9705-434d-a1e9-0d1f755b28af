Index: src/main/java/org/springblade/miniapp/controller/WeChatBusinessCardController.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package org.springblade.miniapp.controller;\r\n\r\nimport com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;\r\nimport io.swagger.v3.oas.annotations.Operation;\r\nimport io.swagger.v3.oas.annotations.Parameter;\r\nimport jakarta.annotation.Resource;\r\nimport jakarta.validation.Valid;\r\nimport lombok.AllArgsConstructor;\r\nimport org.springblade.business.user.entity.BusinessCard;\r\nimport org.springblade.business.user.service.IBusinessCardService;\r\nimport org.springblade.core.mp.support.Condition;\r\nimport org.springblade.core.secure.utils.AuthUtil;\r\nimport org.springblade.core.tool.api.R;\r\nimport org.springblade.core.tool.utils.Func;\r\nimport org.springframework.web.bind.annotation.*;\r\n\r\n@RestController\r\n@AllArgsConstructor\r\n@RequestMapping(\"/blade-chat/card\")\r\<EMAIL>(name = \"小程序个人名片接口\")\r\npublic class WeChatBusinessCardController {\r\n\r\n\t@Resource\r\n\tprivate final IBusinessCardService businessCardService;\r\n\t/**\r\n\t * 新增 名片信息表\r\n\t */\r\n\t@PostMapping(\"/save\")\r\n\t@ApiOperationSupport(order = 4)\r\n\t@Operation(summary = \"新增\", description = \"传入businessCard\")\r\n\tpublic R save(@Valid @RequestBody BusinessCard businessCard) {\r\n\t\t// 设置当前用户ID\r\n\t\tbusinessCard.setCreateUser(AuthUtil.getUserId());\r\n\t\treturn R.status(businessCardService.save(businessCard));\r\n\t}\r\n\r\n\t/**\r\n\t * 修改 名片信息表\r\n\t */\r\n\t@PostMapping(\"/update\")\r\n\t@ApiOperationSupport(order = 5)\r\n\t@Operation(summary = \"修改\", description = \"传入businessCard\")\r\n\tpublic R update(@Valid @RequestBody BusinessCard businessCard) {\r\n\t\t// 确保只能修改自己的名片\r\n\t\tLong userId = AuthUtil.getUserId();\r\n\t\tBusinessCard existingCard = businessCardService.getById(businessCard.getId());\r\n\t\tif (existingCard == null || !userId.equals(existingCard.getCreateUser())) {\r\n\t\t\treturn R.fail(\"无权限修改此名片\");\r\n\t\t}\r\n\t\tbusinessCard.setUpdateUser(userId);\r\n\t\treturn R.status(businessCardService.updateById(businessCard));\r\n\t}\r\n\r\n\t/**\r\n\t * 新增或修改 名片信息表\r\n\t */\r\n\t@PostMapping(\"/submit\")\r\n\t@ApiOperationSupport(order = 6)\r\n\t@Operation(summary = \"新增或修改\", description = \"传入businessCard\")\r\n\tpublic R submit(@Valid @RequestBody BusinessCard businessCard) {\r\n\t\tLong userId = AuthUtil.getUserId();\r\n\r\n\t\tif (businessCard.getId() != null) {\r\n\t\t\t// 修改操作 - 检查权限\r\n\t\t\tBusinessCard existingCard = businessCardService.getById(businessCard.getId());\r\n\t\t\tif (existingCard == null || !userId.equals(existingCard.getCreateUser())) {\r\n\t\t\t\treturn R.fail(\"无权限修改此名片\");\r\n\t\t\t}\r\n\t\t\tbusinessCard.setUpdateUser(userId);\r\n\t\t} else {\r\n\t\t\t// 新增操作 - 检查是否已有名片\r\n\t\t\tBusinessCard query = new BusinessCard();\r\n\t\t\tquery.setCreateUser(userId);\r\n\t\t\tBusinessCard existingCard = businessCardService.getOne(Condition.getQueryWrapper(query));\r\n\t\t\tif (existingCard != null) {\r\n\t\t\t\t// 如果已有名片，则更新现有名片\r\n\t\t\t\tbusinessCard.setId(existingCard.getId());\r\n\t\t\t\tbusinessCard.setUpdateUser(userId);\r\n\t\t\t} else {\r\n\t\t\t\t// 新建名片\r\n\t\t\t\tbusinessCard.setCreateUser(userId);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn R.status(businessCardService.saveOrUpdate(businessCard));\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * 删除 名片信息表\r\n\t */\r\n\t@PostMapping(\"/remove\")\r\n\t@ApiOperationSupport(order = 7)\r\n\t@Operation(summary = \"逻辑删除\", description = \"传入ids\")\r\n\tpublic R remove(@Parameter(description = \"主键集合\", required = true) @RequestParam String ids) {\r\n\t\treturn R.status(businessCardService.deleteLogic(Func.toLongList(ids)));\r\n\t}\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/org/springblade/miniapp/controller/WeChatBusinessCardController.java b/src/main/java/org/springblade/miniapp/controller/WeChatBusinessCardController.java
--- a/src/main/java/org/springblade/miniapp/controller/WeChatBusinessCardController.java	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/src/main/java/org/springblade/miniapp/controller/WeChatBusinessCardController.java	(date 1753954236307)
@@ -1,19 +1,27 @@
 package org.springblade.miniapp.controller;
 
+import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import io.swagger.v3.oas.annotations.Operation;
 import io.swagger.v3.oas.annotations.Parameter;
 import jakarta.annotation.Resource;
 import jakarta.validation.Valid;
 import lombok.AllArgsConstructor;
+import org.springblade.business.post.vo.SupPostVO;
 import org.springblade.business.user.entity.BusinessCard;
 import org.springblade.business.user.service.IBusinessCardService;
+import org.springblade.business.user.vo.BusinessCardVO;
+import org.springblade.core.log.exception.ServiceException;
 import org.springblade.core.mp.support.Condition;
+import org.springblade.core.mp.support.Query;
 import org.springblade.core.secure.utils.AuthUtil;
 import org.springblade.core.tool.api.R;
 import org.springblade.core.tool.utils.Func;
+import org.springblade.miniapp.service.WeChatUserService;
 import org.springframework.web.bind.annotation.*;
 
+import java.util.List;
+
 @RestController
 @AllArgsConstructor
 @RequestMapping("/blade-chat/card")
@@ -95,4 +103,53 @@
 	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
 		return R.status(businessCardService.deleteLogic(Func.toLongList(ids)));
 	}
+
+	/**
+	 * 获取当前用户名片列表
+	 */
+	@GetMapping("/my-card")
+	@ApiOperationSupport(order = 8)
+	@Operation(summary = "获取当前用户名片列表", description = "获取当前登录用户的所有名片信息")
+	public R<List<BusinessCardVO>> getMyCard() {
+		Long userId = AuthUtil.getUserId();
+		BusinessCard query = new BusinessCard();
+		query.setCreateUser(userId);
+
+		List<BusinessCard> myCards = businessCardService.list(Condition.getQueryWrapper(query));
+
+		// 转换为VO对象
+		List<BusinessCardVO> myCardVOs = myCards.stream()
+			.map(card -> {
+				BusinessCardVO vo = new BusinessCardVO();
+				org.springframework.beans.BeanUtils.copyProperties(card, vo);
+				return vo;
+			})
+			.collect(java.util.stream.Collectors.toList());
+
+		return R.data(myCardVOs);
+	}
+
+	/**
+	 * 分页查询名片列表
+	 */
+	@GetMapping("/list")
+	@ApiOperationSupport(order = 10)
+	@Operation(summary = "分页查询名片列表", description = "分页查询名片信息")
+	public R<IPage<BusinessCardVO>> getCardList(BusinessCardVO businessCard, Query query) {
+		IPage<BusinessCardVO> pages = businessCardService.selectBusinessCardPage(Condition.getPage(query), businessCard);
+		return R.data(pages);
+	}
+
+	/**
+	 * 获取名片详情
+	 */
+	@GetMapping("/detail")
+	@ApiOperationSupport(order = 11)
+	@Operation(summary = "获取名片详情", description = "根据ID获取名片详细信息")
+	public R<BusinessCardVO> getCardDetail(@Parameter(description = "名片ID", required = true) @RequestParam Long id) {
+		BusinessCardVO businessCard = new BusinessCardVO();
+		businessCard.setId(id);
+		BusinessCardVO detail = businessCardService.getDetail(businessCard);
+		return R.data(detail);
+	}
 }
Index: ../weapp/pages/local/local.wxss
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* pkg_user/pages/local/local.wxss */\r\n\r\n/* 页面整体 */\r\npage {\r\n  height: 100vh;\r\n  background: #FF7B7B;\r\n  overflow: hidden; /* 禁止页面整体滚动 */\r\n}\r\n\r\n/* 滚动容器 */\r\n.scroll-container {\r\n  position: relative;\r\n  background: #FF7B7B;\r\n  box-sizing: border-box;\r\n  height: 100vh;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  scrollbar-width: none; /* Firefox */\r\n}\r\n\r\n.scroll-container::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  position: relative;\r\n  z-index: 1;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  min-height: calc(100vh - 100rpx); /* 确保内容区域有足够高度 */\r\n  background: #f5f5f5; /* 内容区域保持浅灰色背景 */\r\n}\r\n\r\n/* 轮播图容器样式 */\r\n.banner-container {\r\n  position: relative;\r\n  margin: 0 -20rpx 30rpx -20rpx; /* 负边距让轮播图延伸到屏幕边缘 */\r\n  z-index: 1;\r\n}\r\n\r\n/* 轮播图上方渐变效果 - 与导航栏融合 */\r\n.banner-container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 100rpx;\r\n  background: linear-gradient(to bottom, rgba(255, 107, 107, 0.6), transparent);\r\n  pointer-events: none;\r\n  z-index: 2;\r\n}\r\n\r\n/* 轮播图下方渐变效果 */\r\n.banner-container::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 80rpx;\r\n  background: linear-gradient(to top, #f5f5f5, transparent);\r\n  pointer-events: none;\r\n  z-index: 2;\r\n}\r\n\r\n/* 禁用组件自带的渐变效果，使用页面级别的渐变 */\r\n.banner-container banner-swiper .top-banner::before,\r\n.banner-container banner-swiper .top-banner::after {\r\n  display: none;\r\n}\r\n\r\n/* 确保轮播图内容正确显示 */\r\n.banner-container banner-swiper {\r\n  display: block;\r\n  width: 100%;\r\n}\r\n\r\n.refresh-text {\r\n  position: relative;\r\n  z-index: 2;\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  margin-top: 40rpx;\r\n  opacity: 0.85;\r\n}\r\n\r\n/* 下拉刷新圆环 loading 动画样式 */\r\n.refresh-loading {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  background: #ff6b6b; /* 主题色背景 */\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.circle-loader {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border: 4rpx solid #fff;\r\n  border-top: 4rpx solid #fff;\r\n  border-right: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #ff6b6b;\r\n  border-left: 4rpx solid #ff6b6b;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 8rpx;\r\n  box-shadow: 0 0 8rpx #ff6b6b33;\r\n  background: transparent;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg);}\r\n  100% { transform: rotate(360deg);}\r\n}\r\n\r\n.refresh-text {\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  opacity: 0.85;\r\n  text-shadow: 0 2rpx 8rpx #ff6b6b33;\r\n}\r\n\r\n.refresh-coins {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 10;\r\n  background: #ff6b6b;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  pointer-events: none; /* 不影响下方内容交互 */\r\n}\r\n\r\n.coin {\r\n  position: absolute;\r\n  top: -40rpx;\r\n  animation: coinDrop 0.9s cubic-bezier(0.4,1.4,0.6,1) forwards;\r\n  transform: rotate(var(--coin-rotate, 0deg));\r\n}\r\n\r\n@keyframes coinDrop {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateY(-30rpx) scale(0.7) rotate(var(--coin-rotate, 0deg));\r\n  }\r\n  40% {\r\n    opacity: 1;\r\n    transform: translateY(40rpx) translateX(calc(var(--coin-swing, 0vw) * 0.5)) scale(1.1) rotate(calc(var(--coin-rotate, 0deg) * 0.7));\r\n  }\r\n  70% {\r\n    transform: translateY(70rpx) translateX(var(--coin-swing, 0vw)) scale(0.95) rotate(calc(var(--coin-rotate, 0deg) * 0.3));\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: translateY(80rpx) translateX(var(--coin-swing, 0vw)) scale(1) rotate(0deg);\r\n  }\r\n}\r\n\r\n.refresh-text {\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  opacity: 0.85;\r\n  text-shadow: 0 2rpx 8rpx #ff6b6b33;\r\n  position: relative;\r\n  z-index: 2;\r\n  margin-top: 40rpx;\r\n}\r\n\r\n.refresh-coins-fixed {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  height: 160rpx;\r\n  z-index: 9999;\r\n  background: #FF7B7B;\r\n  pointer-events: none;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.refresh-bottom-area {\r\n  width: 100vw;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  background: #FF7B7B;\r\n  padding-top: 12rpx;\r\n  padding-bottom: 18rpx;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.coin-tray {\r\n  width: 80rpx;\r\n  height: 18rpx;\r\n  background: linear-gradient(90deg, #ffb86b 0%, #ffe066 100%);\r\n  border-radius: 0 0 40rpx 40rpx / 0 0 18rpx 18rpx;\r\n  box-shadow: 0 4rpx 12rpx #ffb86b55;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.refresh-text {\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  opacity: 0.95;\r\n  text-shadow: 0 2rpx 8rpx #ff6b6b33;\r\n  margin-top: 2rpx;\r\n  text-align: center;\r\n}\r\n\r\n.coin {\r\n  position: absolute;\r\n  top: 20rpx;\r\n  animation: coinDropFixed 1.1s cubic-bezier(0.4,1.4,0.6,1) forwards;\r\n  transform: rotate(var(--coin-rotate, 0deg));\r\n}\r\n\r\n@keyframes coinDropFixed {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateY(-40rpx) scale(0.7) rotate(var(--coin-rotate, 0deg));\r\n  }\r\n  40% {\r\n    opacity: 1;\r\n    transform: translateY(60rpx) translateX(calc(var(--coin-swing, 0vw) * 0.5)) scale(1.1) rotate(calc(var(--coin-rotate, 0deg) * 0.7));\r\n  }\r\n  70% {\r\n    transform: translateY(110rpx) translateX(var(--coin-swing, 0vw)) scale(0.95) rotate(calc(var(--coin-rotate, 0deg) * 0.3));\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: translateY(120rpx) translateX(var(--coin-swing, 0vw)) scale(1) rotate(0deg);\r\n  }\r\n} \r\n\r\n/* 吸顶分类栏 */\r\n.sticky-category-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #eee;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 刷新动画已移除 */\r\n\r\n/* 主Tab区域样式 */\r\n.main-tab-section {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 20rpx 30rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.main-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.main-tab-item {\r\n  padding: 16rpx 0;\r\n  margin-right: 40rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  color: #666;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.main-tab-item:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n.main-tab-item.active {\r\n  color: #ff6b6b;\r\n  font-weight: 600;\r\n}\r\n\r\n.main-tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -8rpx;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4rpx;\r\n  background: linear-gradient(90deg, #ff6b6b, #ff8585);\r\n  border-radius: 2rpx;\r\n  animation: slideIn 0.3s ease;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: scaleX(0);\r\n  }\r\n  to {\r\n    transform: scaleX(1);\r\n  }\r\n}\r\n\r\n/* 申请入驻按钮 */\r\n.apply-settle-btn {\r\n  padding: 12rpx 24rpx;\r\n  background: linear-gradient(135deg, #ff6b6b, #ff8585);\r\n  color: #fff;\r\n  font-size: 26rpx;\r\n  font-weight: 500;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.apply-settle-btn:active {\r\n  transform: scale(0.95);\r\n  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);\r\n}\r\n\r\n/* 卡片区域 - 参考index页面样式 */\r\n.card-section {\r\n  background-color: var(--card-background, #fff);\r\n  border-radius: 24rpx;\r\n  margin: 0; /* 移除外边距，让内容填满 */\r\n  overflow: hidden;\r\n}\r\n\r\n.card-section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 30rpx 0 20rpx;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.title-icon {\r\n  font-size: 32rpx;\r\n  margin-right: 12rpx;\r\n}\r\n\r\n.title-text {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n/* 分类标签 */\r\n.category-tabs-scroll {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.category-tabs {\r\n  display: flex;\r\n  padding: 0 10rpx;\r\n}\r\n\r\n.category-tabs.single-tab {\r\n  justify-content: center;\r\n}\r\n\r\n.tab-item {\r\n  flex-shrink: 0;\r\n  padding: 16rpx 28rpx;\r\n  margin: 0 12rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 24rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  text-align: center;\r\n  white-space: nowrap;\r\n  border: 2rpx solid transparent;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-item.active {\r\n  background: linear-gradient(135deg, #ff6b6b, #ff8585);\r\n  color: #fff;\r\n  transform: scale(1.05);\r\n  border-color: rgba(255, 107, 107, 0.3);\r\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.25);\r\n}\r\n\r\n.tab-item:first-child {\r\n  margin-left: 0;\r\n}\r\n\r\n.tab-item:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n/* 机构列表容器 */\r\n.institutions-container {\r\n  background-color: #f5f6f7;\r\n  padding: 0;\r\n}\r\n\r\n/* 底部状态区域 */\r\n.bottom-status {\r\n  padding: 40rpx 0;\r\n  text-align: center;\r\n}\r\n\r\n.loading-more {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.loading-more.preloading {\r\n  color: #ff6b6b;\r\n}\r\n\r\n.reached-bottom,\r\n.no-more {\r\n  font-size: 26rpx;\r\n  color: #999;\r\n  padding: 20rpx;\r\n  background: #f8f8f8;\r\n  border-radius: 12rpx;\r\n  margin: 0 20rpx;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 120rpx 40rpx;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin: 20rpx;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 120rpx;\r\n  margin-bottom: 30rpx;\r\n  opacity: 0.6;\r\n  animation: float 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-10rpx);\r\n  }\r\n}\r\n\r\n.empty-text {\r\n  font-size: 32rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.empty-tip {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n  text-align: center;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 旧样式已移除，使用组件化设计 */\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pages/local/local.wxss b/../weapp/pages/local/local.wxss
--- a/../weapp/pages/local/local.wxss	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pages/local/local.wxss	(date 1753949769798)
@@ -410,7 +410,6 @@
 
 /* 机构列表容器 */
 .institutions-container {
-  background-color: #f5f6f7;
   padding: 0;
 }
 
Index: ../weapp/pkg_user/pages/card-detail/card-detail.json
===================================================================
diff --git a/../weapp/pkg_user/pages/card-detail/card-detail.json b/../weapp/pkg_user/pages/card-detail/card-detail.json
deleted file mode 100644
--- a/../weapp/pkg_user/pages/card-detail/card-detail.json	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ /dev/null	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
@@ -1,6 +0,0 @@
-{
-  "navigationBarTitleText": "名片详情",
-  "navigationBarBackgroundColor": "#ff8382",
-  "navigationBarTextStyle": "white",
-  "backgroundColor": "#f5f5f5"
-}
Index: ../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.json
===================================================================
diff --git a/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.json b/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.json
deleted file mode 100644
--- a/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.json	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ /dev/null	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
@@ -1,8 +0,0 @@
-{
-  "navigationBarTitleText": "添加名片",
-  "navigationBarBackgroundColor": "#FF8383",
-  "navigationBarTextStyle": "white",
-  "backgroundColor": "#f5f5f5",
-  "backgroundTextStyle": "dark",
-  "enablePullDownRefresh": false
-} 
\ No newline at end of file
Index: ../weapp/utils/request.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址\r\nconst BASE_URL = 'http://**************'; // 替换为您的后端API地址\r\n\r\nconst { handleLoginExpired } = require('./loginHandler');\r\n\r\n// 请求拦截器\r\nconst requestInterceptor = (config) => {\r\n  const token = wx.getStorageSync('token').value;\r\n  const openId = wx.getStorageSync('openId');\r\n\r\n  config.header = {\r\n    ...config.header,\r\n    'Tenant-Id' :\"000000\",\r\n    'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,\r\n  };\r\n\r\n  if (token) {\r\n    config.header = {\r\n      ...config.header,\r\n      'Blade-Auth': `Bearer ${token}`\r\n    };\r\n  }\r\n\r\n  // 添加OpenID头\r\n  if (openId) {\r\n    config.header = {\r\n      ...config.header,\r\n      'X-Open-ID': openId\r\n    };\r\n  }\r\n  \r\n  return config;\r\n};\r\n\r\n// 响应拦截器\r\nconst responseInterceptor = (response) => {\r\n  const { data } = response;\r\n  // 处理token过期\r\n  if (data.code === 401) {\r\n    handleLoginExpired();\r\n    return Promise.reject(new Error('登录已过期，请重新登录'));\r\n  }\r\n  \r\n  return data;\r\n};\r\n\r\n// 刷新token\r\nconst refreshToken = () => {\r\n  return new Promise((resolve, reject) => {\r\n    const refreshToken = wx.getStorageSync('refreshToken');\r\n    if (!refreshToken) {\r\n      reject(new Error('未找到刷新token'));\r\n      return;\r\n    }\r\n\r\n    wx.request({\r\n      url: `${BASE_URL}/blade-auth/token`,\r\n      method: 'POST',\r\n      data: {\r\n        grantType: 'refresh_token',\r\n        refreshToken: refreshToken\r\n      },\r\n      success: (res) => {\r\n        if (res.data.success) {\r\n          const { accessToken, refreshToken } = res.data.data;\r\n          wx.setStorageSync('token', accessToken);\r\n          wx.setStorageSync('refreshToken', refreshToken);\r\n          resolve(accessToken);\r\n        } else {\r\n          reject(new Error(res.data.msg));\r\n        }\r\n      },\r\n      fail: reject\r\n    });\r\n  });\r\n};\r\n\r\n// 统一请求方法\r\nconst request = (options) => {\r\n  const config = requestInterceptor(options);\r\n  const fullUrl = `${BASE_URL}${config.url}`;\r\n\r\n  console.log('=== 发起网络请求 ===');\r\n  console.log('请求URL:', fullUrl);\r\n  console.log('请求方法:', config.method);\r\n  console.log('请求头:', JSON.stringify(config.header, null, 2));\r\n  console.log('请求数据:', JSON.stringify(config.data, null, 2));\r\n  console.log('BASE_URL:', BASE_URL);\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const startTime = Date.now();\r\n\r\n    wx.request({\r\n      ...config,\r\n      url: fullUrl,\r\n      success: (res) => {\r\n        const endTime = Date.now();\r\n        console.log(`=== 网络请求成功 (耗时: ${endTime - startTime}ms) ===`);\r\n        console.log('响应状态码:', res.statusCode);\r\n        console.log('响应头:', JSON.stringify(res.header, null, 2));\r\n        console.log('响应数据:', JSON.stringify(res.data, null, 2));\r\n\r\n        try {\r\n          const result = responseInterceptor(res);\r\n          resolve(result);\r\n        } catch (error) {\r\n          console.error('响应拦截器处理失败:', error);\r\n          reject(error);\r\n        }\r\n      },\r\n      fail: (err) => {\r\n        const endTime = Date.now();\r\n        console.error(`=== 网络请求失败 (耗时: ${endTime - startTime}ms) ===`);\r\n        console.error('请求URL:', fullUrl);\r\n        console.error('错误信息:', JSON.stringify(err, null, 2));\r\n        console.error('错误类型:', err.errMsg);\r\n\r\n        // 增强错误信息\r\n        const enhancedError = new Error(`网络请求失败: ${err.errMsg || '未知错误'}`);\r\n        enhancedError.originalError = err;\r\n        enhancedError.requestUrl = fullUrl;\r\n        enhancedError.requestConfig = config;\r\n\r\n        reject(enhancedError);\r\n      }\r\n    });\r\n  });\r\n};\r\n\r\nmodule.exports = {\r\n  request,\r\n  refreshToken\r\n}; 
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/utils/request.js b/../weapp/utils/request.js
--- a/../weapp/utils/request.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/utils/request.js	(date 1753950769052)
@@ -1,5 +1,5 @@
 // const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址
-const BASE_URL = 'http://**************'; // 替换为您的后端API地址
+const BASE_URL = 'http://localhost'; // 替换为您的后端API地址
 
 const { handleLoginExpired } = require('./loginHandler');
 
Index: ../weapp/pkg_merchant/config/api.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/**\r\n * API配置文件\r\n */\r\nconst config = {\r\n  // API基础地址\r\n  baseUrl: 'http://**************',\r\n  \r\n  // 文件上传相关接口\r\n  fileUpload: {\r\n    // 单文件上传\r\n    upload: '/blade-system/file-upload/upload',\r\n    // 批量文件上传\r\n    uploadBatch: '/blade-system/file-upload/upload-batch',\r\n    // 获取文件URL\r\n    getFileUrl: '/blade-system/file-upload/url',\r\n    // 删除文件\r\n    removeFile: '/blade-system/file-upload/remove',\r\n    // 获取文件列表\r\n    getFileList: '/blade-system/file-upload/list',\r\n    // 根据业务获取文件\r\n    getFilesByBusiness: '/blade-system/file-upload/business',\r\n    // 获取文件统计\r\n    getFileStats: '/blade-system/file-upload/stats',\r\n    // 清理过期文件\r\n    cleanExpiredFiles: '/blade-system/file-upload/clean-expired',\r\n    // 存储配置\r\n    storageConfig: '/blade-system/file-upload/storage-config'\r\n  },\r\n  \r\n  // 认证相关\r\n  auth: {\r\n    tenantId: '000000',\r\n    basicAuth: 'Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U='\r\n  },\r\n  \r\n  // 文件上传配置\r\n  upload: {\r\n    // 最大文件大小（字节）\r\n    maxFileSize: 10 * 1024 * 1024, // 10MB\r\n    // 支持的文件类型\r\n    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\r\n    // 最大图片数量\r\n    maxImageCount: 6,\r\n    // 上传来源\r\n    uploadSource: 'miniapp'\r\n  }\r\n};\r\n\r\nmodule.exports = config; 
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_merchant/config/api.js b/../weapp/pkg_merchant/config/api.js
--- a/../weapp/pkg_merchant/config/api.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_merchant/config/api.js	(date *************)
@@ -3,7 +3,7 @@
  */
 const config = {
   // API基础地址
-  baseUrl: 'http://**************',
+  baseUrl: 'http://localhost',
   
   // 文件上传相关接口
   fileUpload: {
Index: ../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>Component({\r\n  properties: {\r\n    cardList: {\r\n      type: Array,\r\n      value: []\r\n    }\r\n  },\r\n  data: {\r\n    flippedIndex: -1 // 当前反转的卡片索引\r\n  },\r\n  methods: {\r\n    onCardTap(e) {\r\n      const index = e.currentTarget.dataset.index;\r\n      this.setData({\r\n        flippedIndex: this.data.flippedIndex === index ? -1 : index\r\n      });\r\n    },\r\n    \r\n    // 跳转到添加名片页面\r\n    onAddCard() {\r\n      wx.navigateTo({\r\n        url: '/pkg_user/pages/add-cardInfo/add-cardInfo'\r\n      });\r\n    },\r\n    \r\n    // 隐藏卡片\r\n    onHideCard(e) {\r\n      const index = e.currentTarget.dataset.index;\r\n      wx.showModal({\r\n        title: '提示',\r\n        content: '确定要隐藏这张名片吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 这里可以调用API隐藏卡片\r\n            wx.showToast({\r\n              title: '已隐藏',\r\n              icon: 'success'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 编辑卡片\r\n    onEditCard(e) {\r\n      const index = e.currentTarget.dataset.index;\r\n      const card = this.data.cardList[index];\r\n      wx.showToast({\r\n        title: '编辑功能开发中',\r\n        icon: 'none'\r\n      });\r\n      // 这里可以跳转到编辑页面\r\n      // wx.navigateTo({\r\n      //   url: `/pkg_user/pages/edit-card/edit-card?cardId=${card.cardId}`\r\n      // });\r\n    },\r\n    \r\n    // 删除卡片\r\n    onDeleteCard(e) {\r\n      const index = e.currentTarget.dataset.index;\r\n      const card = this.data.cardList[index];\r\n      wx.showModal({\r\n        title: '确认删除',\r\n        content: `确定要删除\"${card.company}\"的名片吗？此操作不可恢复。`,\r\n        confirmColor: '#ff8080',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 这里可以调用API删除卡片\r\n            wx.showToast({\r\n              title: '已删除',\r\n              icon: 'success'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }\r\n});
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.js b/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.js
--- a/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.js	(date 1753954447737)
@@ -1,3 +1,6 @@
+// 引入名片管理 Store
+const businessCardStore = require('../../../../../stores/businessCardStore.js');
+
 Component({
   properties: {
     cardList: {
@@ -15,23 +18,30 @@
         flippedIndex: this.data.flippedIndex === index ? -1 : index
       });
     },
-    
+
     // 跳转到添加名片页面
     onAddCard() {
       wx.navigateTo({
-        url: '/pkg_user/pages/add-cardInfo/add-cardInfo'
+        url: '/pkg_user/pages/user-card/add-cardInfo/add-cardInfo'
       });
     },
-    
-    // 隐藏卡片
+
+    // 隐藏卡片（暂时保持原有逻辑，可以后续扩展为修改可见性状态）
     onHideCard(e) {
       const index = e.currentTarget.dataset.index;
+      const card = this.data.cardList[index];
+
       wx.showModal({
         title: '提示',
         content: '确定要隐藏这张名片吗？',
         success: (res) => {
           if (res.confirm) {
-            // 这里可以调用API隐藏卡片
+            // 触发父组件事件，从列表中移除该卡片
+            this.triggerEvent('hideCard', {
+              index: index,
+              cardId: card.cardId || card.id
+            });
+
             wx.showToast({
               title: '已隐藏',
               icon: 'success'
@@ -40,36 +50,74 @@
         }
       });
     },
-    
+
     // 编辑卡片
     onEditCard(e) {
       const index = e.currentTarget.dataset.index;
       const card = this.data.cardList[index];
-      wx.showToast({
-        title: '编辑功能开发中',
-        icon: 'none'
+
+      // 跳转到编辑页面，传递卡片ID
+      wx.navigateTo({
+        url: `/pkg_user/pages/user-card/add-cardInfo/add-cardInfo?mode=edit&cardId=${card.cardId || card.id}`
       });
-      // 这里可以跳转到编辑页面
-      // wx.navigateTo({
-      //   url: `/pkg_user/pages/edit-card/edit-card?cardId=${card.cardId}`
-      // });
     },
-    
+
     // 删除卡片
-    onDeleteCard(e) {
+    async onDeleteCard(e) {
       const index = e.currentTarget.dataset.index;
       const card = this.data.cardList[index];
+      const cardId = card.cardId || card.id;
+
+      if (!cardId) {
+        wx.showToast({
+          title: '卡片ID不存在',
+          icon: 'none'
+        });
+        return;
+      }
+
       wx.showModal({
         title: '确认删除',
-        content: `确定要删除"${card.company}"的名片吗？此操作不可恢复。`,
+        content: `确定要删除"${card.company || card.name}"的名片吗？此操作不可恢复。`,
         confirmColor: '#ff8080',
-        success: (res) => {
+        success: async (res) => {
           if (res.confirm) {
-            // 这里可以调用API删除卡片
-            wx.showToast({
-              title: '已删除',
-              icon: 'success'
-            });
+            // 显示加载状态
+            wx.showLoading({
+              title: '删除中...'
+            });
+
+            try {
+              // 调用删除API
+              const result = await businessCardStore.deleteCard(cardId.toString());
+
+              wx.hideLoading();
+
+              if (result.success) {
+                // 删除成功，触发父组件事件更新列表
+                this.triggerEvent('deleteCard', {
+                  index: index,
+                  cardId: cardId
+                });
+
+                wx.showToast({
+                  title: '删除成功',
+                  icon: 'success'
+                });
+              } else {
+                wx.showToast({
+                  title: result.message || '删除失败',
+                  icon: 'none'
+                });
+              }
+            } catch (error) {
+              wx.hideLoading();
+              console.error('删除名片失败:', error);
+              wx.showToast({
+                title: '删除失败',
+                icon: 'none'
+              });
+            }
           }
         }
       });
Index: ../weapp/pkg_merchant/utils/request.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址\r\nconst BASE_URL = 'http://**************'; // 替换为您的后端API地址\r\n\r\nconst { handleLoginExpired } = require('../../utils/loginHandler');\r\n\r\n// 请求拦截器\r\nconst requestInterceptor = (config) => {\r\n  const token = wx.getStorageSync('token').value;\r\n  const openId = wx.getStorageSync('openId');\r\n\r\n  config.header = {\r\n    ...config.header,\r\n    'Tenant-Id' :\"000000\",\r\n    'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,\r\n  };\r\n\r\n  if (token) {\r\n    config.header = {\r\n      ...config.header,\r\n      'Blade-Auth': `Bearer ${token}`\r\n    };\r\n  }\r\n\r\n  // 添加OpenID头\r\n  if (openId) {\r\n    config.header = {\r\n      ...config.header,\r\n      'X-Open-ID': openId\r\n    };\r\n  }\r\n  \r\n  return config;\r\n};\r\n\r\n// 响应拦截器\r\nconst responseInterceptor = (response) => {\r\n  const { data } = response;\r\n  // 处理token过期\r\n  if (data.code === 401) {\r\n    handleLoginExpired();\r\n    return Promise.reject(new Error('登录已过期，请重新登录'));\r\n  }\r\n  \r\n  return data;\r\n};\r\n\r\n// 刷新token\r\nconst refreshToken = () => {\r\n  return new Promise((resolve, reject) => {\r\n    const refreshToken = wx.getStorageSync('refreshToken');\r\n    if (!refreshToken) {\r\n      reject(new Error('未找到刷新token'));\r\n      return;\r\n    }\r\n\r\n    wx.request({\r\n      url: `${BASE_URL}/blade-auth/token`,\r\n      method: 'POST',\r\n      data: {\r\n        grantType: 'refresh_token',\r\n        refreshToken: refreshToken\r\n      },\r\n      success: (res) => {\r\n        if (res.data.success) {\r\n          const { accessToken, refreshToken } = res.data.data;\r\n          wx.setStorageSync('token', accessToken);\r\n          wx.setStorageSync('refreshToken', refreshToken);\r\n          resolve(accessToken);\r\n        } else {\r\n          reject(new Error(res.data.msg));\r\n        }\r\n      },\r\n      fail: reject\r\n    });\r\n  });\r\n};\r\n\r\n// 统一请求方法\r\nconst request = (options) => {\r\n  const config = requestInterceptor(options);\r\n  console.log(config);  \r\n  // accessToken: \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aM2NpDQsCGbjX-KXssWkyHv4S6WRWhKwBM3rpuy3pOk\"\r\n\r\n  return new Promise((resolve, reject) => {\r\n    wx.request({\r\n      ...config,\r\n      url: `${BASE_URL}${config.url}`,\r\n        success: (res) => {\r\n        resolve(responseInterceptor(res));\r\n      },\r\n      fail: (err) => {\r\n        reject(err);\r\n      }\r\n    });\r\n  });\r\n};\r\n\r\nmodule.exports = {\r\n  request,\r\n  refreshToken\r\n}; 
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_merchant/utils/request.js b/../weapp/pkg_merchant/utils/request.js
--- a/../weapp/pkg_merchant/utils/request.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_merchant/utils/request.js	(date *************)
@@ -1,5 +1,5 @@
 // const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址
-const BASE_URL = 'http://**************'; // 替换为您的后端API地址
+const BASE_URL = 'http://localhost'; // 替换为您的后端API地址
 
 const { handleLoginExpired } = require('../../utils/loginHandler');
 
Index: ../weapp/pkg_user/pages/user-card/user-card.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>Page({\r\n  data: {\r\n    userCards: [\r\n      {\r\n        cardId: 'CARD001', // 名片ID\r\n        company: '上海觅知房地产有限公司', // 公司名称\r\n        position: '总经理', // 职位\r\n        businessIntro: '专注于房地产开发与销售，提供专业的房产咨询服务，致力于为客户创造价值。', // 业务简介\r\n        name: '觅知君', // 姓名\r\n        gender: 1, // 性别(0-保密，1-男，2-女)\r\n        phone: '123 4567 8910', // 电话\r\n        address: '上海市浦东新区秀浦路11号', // 地址\r\n        email: '<EMAIL>', // 邮箱\r\n        website: 'www.51miz.com', // 网址\r\n        wechat: 'mizhi001', // 微信\r\n        avatar: '/assets/images/def-avatar.png', // 头像\r\n        logo: '/assets/images/bot-avatar.png', // 公司logo\r\n        qrCode: '/assets/images/bot-avatar.png', // 二维码\r\n        images: ['https://via.placeholder.com/400x300'], // 图片\r\n        videos: ['sample_video.mp4'], // 视频\r\n        remark: '房地产行业资深从业者', // 备注信息\r\n        isPublic: 1, // 是否公开(0-否，1-是)\r\n        updateTime: '2024-01-20 14:30', // 更新时间\r\n        auditStatus: 1, // 审核状态(0-待审核，1-已通过，2-未通过)\r\n        cardType: '房地产' // 名片类型\r\n      },\r\n      {\r\n        cardId: 'CARD002',\r\n        company: '北京科技创新有限公司',\r\n        position: '技术总监',\r\n        businessIntro: '专注于人工智能和大数据技术研发，为企业提供智能化解决方案。',\r\n        name: '李明',\r\n        gender: 1,\r\n        phone: '138 0000 1234',\r\n        address: '北京市朝阳区建国路88号SOHO现代城',\r\n        email: '<EMAIL>',\r\n        website: 'www.techinnov.com',\r\n        wechat: 'liming_tech',\r\n        avatar: '/assets/images/def-avatar.png',\r\n        logo: '/assets/images/bot-avatar.png',\r\n        qrCode: '/assets/images/bot-avatar.png',\r\n        images: ['https://via.placeholder.com/400x300'],\r\n        videos: ['tech_demo.mp4'],\r\n        remark: '技术创新引领者',\r\n        isPublic: 1,\r\n        updateTime: '2024-01-19 16:45',\r\n        auditStatus: 1,\r\n        cardType: '科技'\r\n      },\r\n      {\r\n        cardId: 'CARD003',\r\n        company: '深圳美食文化传播有限公司',\r\n        position: '行政总厨',\r\n        businessIntro: '传承中华美食文化，创新现代烹饪技艺，为客户提供高品质餐饮服务。',\r\n        name: '王美食',\r\n        gender: 2,\r\n        phone: '159 8888 6666',\r\n        address: '深圳市南山区科技园南区深圳湾科技生态园',\r\n        email: '<EMAIL>',\r\n        website: 'www.deliciousfood.com',\r\n        wechat: 'chef_wang',\r\n        avatar: '/assets/images/def-avatar.png',\r\n        logo: '/assets/images/bot-avatar.png',\r\n        qrCode: '/assets/images/bot-avatar.png',\r\n        images: ['https://via.placeholder.com/400x300'],\r\n        videos: ['cooking_show.mp4'],\r\n        remark: '美食艺术家',\r\n        isPublic: 1,\r\n        updateTime: '2024-01-18 10:20',\r\n        auditStatus: 1,\r\n        cardType: '餐饮'\r\n      },\r\n      {\r\n        cardId: 'CARD004',\r\n        company: '广州金融投资集团',\r\n        position: '投资经理',\r\n        businessIntro: '专业从事股权投资和资产管理，为客户提供全方位的财富管理服务。',\r\n        name: '陈投资',\r\n        gender: 1,\r\n        phone: '186 6666 8888',\r\n        address: '广州市天河区珠江新城花城大道85号',\r\n        email: '<EMAIL>',\r\n        website: 'www.gzfinance.com',\r\n        wechat: 'chen_invest',\r\n        avatar: '/assets/images/def-avatar.png',\r\n        logo: '/assets/images/bot-avatar.png',\r\n        qrCode: '/assets/images/bot-avatar.png',\r\n        images: ['https://via.placeholder.com/400x300'],\r\n        videos: ['investment_intro.mp4'],\r\n        remark: '财富管理专家',\r\n        isPublic: 0,\r\n        updateTime: '2024-01-17 09:15',\r\n        auditStatus: 2,\r\n        cardType: '金融'\r\n      },\r\n      {\r\n        cardId: 'CARD005',\r\n        company: '杭州教育科技有限公司',\r\n        position: '教育总监',\r\n        businessIntro: '致力于在线教育平台开发，为学生提供优质的学习资源和个性化教学服务。',\r\n        name: '张教育',\r\n        gender: 2,\r\n        phone: '177 7777 9999',\r\n        address: '杭州市西湖区文三路259号昌地火炬大厦',\r\n        email: '<EMAIL>',\r\n        website: 'www.hzedu.com',\r\n        wechat: 'zhang_edu',\r\n        avatar: '/assets/images/def-avatar.png',\r\n        logo: '/assets/images/bot-avatar.png',\r\n        qrCode: '/assets/images/bot-avatar.png',\r\n        images: ['https://via.placeholder.com/400x300'],\r\n        videos: ['education_demo.mp4'],\r\n        remark: '教育创新践行者',\r\n        isPublic: 1,\r\n        updateTime: '2024-01-16 14:00',\r\n        auditStatus: 0,\r\n        cardType: '教育'\r\n      },\r\n      {\r\n        cardId: 'CARD006',\r\n        company: '成都医疗健康科技有限公司',\r\n        position: '医疗总监',\r\n        businessIntro: '专注于智慧医疗解决方案，结合AI技术提升医疗服务质量和效率。',\r\n        name: '刘医生',\r\n        gender: 1,\r\n        phone: '199 0000 5555',\r\n        address: '成都市高新区天府大道中段1388号',\r\n        email: '<EMAIL>',\r\n        website: 'www.cdmedical.com',\r\n        wechat: 'dr_liu',\r\n        avatar: '/assets/images/def-avatar.png',\r\n        logo: '/assets/images/bot-avatar.png',\r\n        qrCode: '/assets/images/bot-avatar.png',\r\n        images: ['https://via.placeholder.com/400x300'],\r\n        videos: ['medical_tech.mp4'],\r\n        remark: '智慧医疗先行者',\r\n        isPublic: 1,\r\n        updateTime: '2024-01-15 11:30',\r\n        auditStatus: 1,\r\n        cardType: '医疗'\r\n      }\r\n    ]\r\n  },\r\n\r\n  onLoad() {\r\n    console.log('用户卡片页面加载', this.data.userCards);\r\n  },\r\n\r\n  // 处理添加名片事件\r\n  onAddCard(e) {\r\n    const newCard = e.detail;\r\n    console.log('添加新名片:', newCard);\r\n    \r\n    // 将新名片添加到列表中\r\n    const userCards = [...this.data.userCards, newCard];\r\n    this.setData({\r\n      userCards: userCards\r\n    });\r\n    \r\n    console.log('名片列表已更新，总数:', userCards.length);\r\n  }\r\n});\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/pages/user-card/user-card.js b/../weapp/pkg_user/pages/user-card/user-card.js
--- a/../weapp/pkg_user/pages/user-card/user-card.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/pages/user-card/user-card.js	(date 1753954072781)
@@ -1,162 +1,255 @@
+// 引入名片管理 Store
+const businessCardStore = require('../../../stores/businessCardStore.js');
+
 Page({
   data: {
-    userCards: [
-      {
-        cardId: 'CARD001', // 名片ID
-        company: '上海觅知房地产有限公司', // 公司名称
-        position: '总经理', // 职位
-        businessIntro: '专注于房地产开发与销售，提供专业的房产咨询服务，致力于为客户创造价值。', // 业务简介
-        name: '觅知君', // 姓名
-        gender: 1, // 性别(0-保密，1-男，2-女)
-        phone: '123 4567 8910', // 电话
-        address: '上海市浦东新区秀浦路11号', // 地址
-        email: '<EMAIL>', // 邮箱
-        website: 'www.51miz.com', // 网址
-        wechat: 'mizhi001', // 微信
-        avatar: '/assets/images/def-avatar.png', // 头像
-        logo: '/assets/images/bot-avatar.png', // 公司logo
-        qrCode: '/assets/images/bot-avatar.png', // 二维码
-        images: ['https://via.placeholder.com/400x300'], // 图片
-        videos: ['sample_video.mp4'], // 视频
-        remark: '房地产行业资深从业者', // 备注信息
-        isPublic: 1, // 是否公开(0-否，1-是)
-        updateTime: '2024-01-20 14:30', // 更新时间
-        auditStatus: 1, // 审核状态(0-待审核，1-已通过，2-未通过)
-        cardType: '房地产' // 名片类型
-      },
-      {
-        cardId: 'CARD002',
-        company: '北京科技创新有限公司',
-        position: '技术总监',
-        businessIntro: '专注于人工智能和大数据技术研发，为企业提供智能化解决方案。',
-        name: '李明',
-        gender: 1,
-        phone: '138 0000 1234',
-        address: '北京市朝阳区建国路88号SOHO现代城',
-        email: '<EMAIL>',
-        website: 'www.techinnov.com',
-        wechat: 'liming_tech',
-        avatar: '/assets/images/def-avatar.png',
-        logo: '/assets/images/bot-avatar.png',
-        qrCode: '/assets/images/bot-avatar.png',
-        images: ['https://via.placeholder.com/400x300'],
-        videos: ['tech_demo.mp4'],
-        remark: '技术创新引领者',
-        isPublic: 1,
-        updateTime: '2024-01-19 16:45',
-        auditStatus: 1,
-        cardType: '科技'
-      },
-      {
-        cardId: 'CARD003',
-        company: '深圳美食文化传播有限公司',
-        position: '行政总厨',
-        businessIntro: '传承中华美食文化，创新现代烹饪技艺，为客户提供高品质餐饮服务。',
-        name: '王美食',
-        gender: 2,
-        phone: '159 8888 6666',
-        address: '深圳市南山区科技园南区深圳湾科技生态园',
-        email: '<EMAIL>',
-        website: 'www.deliciousfood.com',
-        wechat: 'chef_wang',
-        avatar: '/assets/images/def-avatar.png',
-        logo: '/assets/images/bot-avatar.png',
-        qrCode: '/assets/images/bot-avatar.png',
-        images: ['https://via.placeholder.com/400x300'],
-        videos: ['cooking_show.mp4'],
-        remark: '美食艺术家',
-        isPublic: 1,
-        updateTime: '2024-01-18 10:20',
-        auditStatus: 1,
-        cardType: '餐饮'
-      },
-      {
-        cardId: 'CARD004',
-        company: '广州金融投资集团',
-        position: '投资经理',
-        businessIntro: '专业从事股权投资和资产管理，为客户提供全方位的财富管理服务。',
-        name: '陈投资',
-        gender: 1,
-        phone: '186 6666 8888',
-        address: '广州市天河区珠江新城花城大道85号',
-        email: '<EMAIL>',
-        website: 'www.gzfinance.com',
-        wechat: 'chen_invest',
-        avatar: '/assets/images/def-avatar.png',
-        logo: '/assets/images/bot-avatar.png',
-        qrCode: '/assets/images/bot-avatar.png',
-        images: ['https://via.placeholder.com/400x300'],
-        videos: ['investment_intro.mp4'],
-        remark: '财富管理专家',
-        isPublic: 0,
-        updateTime: '2024-01-17 09:15',
-        auditStatus: 2,
-        cardType: '金融'
-      },
-      {
-        cardId: 'CARD005',
-        company: '杭州教育科技有限公司',
-        position: '教育总监',
-        businessIntro: '致力于在线教育平台开发，为学生提供优质的学习资源和个性化教学服务。',
-        name: '张教育',
-        gender: 2,
-        phone: '177 7777 9999',
-        address: '杭州市西湖区文三路259号昌地火炬大厦',
-        email: '<EMAIL>',
-        website: 'www.hzedu.com',
-        wechat: 'zhang_edu',
-        avatar: '/assets/images/def-avatar.png',
-        logo: '/assets/images/bot-avatar.png',
-        qrCode: '/assets/images/bot-avatar.png',
-        images: ['https://via.placeholder.com/400x300'],
-        videos: ['education_demo.mp4'],
-        remark: '教育创新践行者',
-        isPublic: 1,
-        updateTime: '2024-01-16 14:00',
-        auditStatus: 0,
-        cardType: '教育'
-      },
-      {
-        cardId: 'CARD006',
-        company: '成都医疗健康科技有限公司',
-        position: '医疗总监',
-        businessIntro: '专注于智慧医疗解决方案，结合AI技术提升医疗服务质量和效率。',
-        name: '刘医生',
-        gender: 1,
-        phone: '199 0000 5555',
-        address: '成都市高新区天府大道中段1388号',
-        email: '<EMAIL>',
-        website: 'www.cdmedical.com',
-        wechat: 'dr_liu',
-        avatar: '/assets/images/def-avatar.png',
-        logo: '/assets/images/bot-avatar.png',
-        qrCode: '/assets/images/bot-avatar.png',
-        images: ['https://via.placeholder.com/400x300'],
-        videos: ['medical_tech.mp4'],
-        remark: '智慧医疗先行者',
-        isPublic: 1,
-        updateTime: '2024-01-15 11:30',
-        auditStatus: 1,
-        cardType: '医疗'
-      }
-    ]
+    // 后端数据
+    myCard: null,
+    myPosts: [],
+    loading: false,
+    isFirstLoad: true, // 标记是否是第一次加载
+
+    userCards: []
   },
 
   onLoad() {
     console.log('用户卡片页面加载', this.data.userCards);
+    // 后台加载真实数据
+    this.loadRealData();
+  },
+
+  onShow() {
+    // 页面显示时刷新数据（但避免重复加载）
+    // 只有当不是第一次加载时才刷新
+    if (!this.data.isFirstLoad) {
+      this.loadRealData();
+    } else {
+      this.setData({ isFirstLoad: false });
+    }
+  },
+
+  /**
+   * 加载真实数据（后台接口）
+   */
+  async loadRealData() {
+    this.setData({ loading: true });
+
+    try {
+      // 并行加载我的名片和名片列表
+      const [cardResult] = await Promise.all([
+        this.loadMyCard()
+      ]);
+
+      console.log('我的名片:', cardResult);
+
+    } catch (error) {
+      console.error('加载数据失败:', error);
+    } finally {
+      this.setData({ loading: false });
+    }
+  },
+
+
+
+  /**
+   * 加载我的名片列表
+   */
+  async loadMyCard() {
+    try {
+      const result = await businessCardStore.getMyCard();
+
+      if (result.success && result.data) {
+        this.setData({
+          myCard: result.data
+        });
+
+        // 处理名片列表数据
+        if (Array.isArray(result.data) && result.data.length > 0) {
+          // 转换所有名片为显示格式
+          const userCards = result.data.map(card => this.convertToDisplayFormat(card));
+          console.log('转换后的名片列表:', userCards);
+          this.setData({
+            userCards: userCards
+          });
+          console.log('设置userCards后:', this.data.userCards);
+        } else {
+          // 如果没有名片数据，设置空数组
+          console.log('没有名片数据，设置空数组');
+          this.setData({
+            userCards: []
+          });
+        }
+      } else {
+        // 如果获取失败，设置空数组
+        console.log('获取名片失败，设置空数组');
+        this.setData({
+          userCards: []
+        });
+      }
+
+      return result;
+    } catch (error) {
+      console.error('加载名片异常:', error);
+      this.setData({
+        userCards: []
+      });
+      return { success: false, message: error.message };
+    }
+  },
+
+
+  /**
+   * 转换后端数据格式为前端展示格式
+   */
+  convertToDisplayFormat(backendCard) {
+    return {
+      cardId: backendCard.id,
+      company: backendCard.company || '未填写公司',
+      position: backendCard.jobTitle || '未填写职位',
+      businessIntro: backendCard.businessProfile || '未填写业务简介',
+      name: backendCard.fullName || '未填写姓名',
+      gender: backendCard.gender || 0,
+      phone: backendCard.phone || '未填写电话',
+      address: backendCard.address || '未填写地址',
+      email: backendCard.email || '未填写邮箱',
+      website: backendCard.website || '未填写网址',
+      wechat: backendCard.weixin || '未填写微信',
+      avatar: backendCard.avatar || '/assets/images/def-avatar.png',
+      logo: '/assets/images/bot-avatar.png',
+      qrCode: '/assets/images/bot-avatar.png',
+      images: backendCard.images ? backendCard.images.split(',') : [],
+      videos: backendCard.video ? [backendCard.video] : [],
+      remark: backendCard.description || '暂无备注',
+      isPublic: backendCard.isPublic || 0,
+      updateTime: backendCard.updateTime || new Date().toLocaleString(),
+      auditStatus: backendCard.auditStatus || 0,
+      cardType: '个人名片'
+    };
   },
 
   // 处理添加名片事件
   onAddCard(e) {
     const newCard = e.detail;
     console.log('添加新名片:', newCard);
-    
+
     // 将新名片添加到列表中
     const userCards = [...this.data.userCards, newCard];
     this.setData({
       userCards: userCards
     });
-    
+
     console.log('名片列表已更新，总数:', userCards.length);
+  },
+
+  /**
+   * 保存名片到后端
+   */
+  async saveCardToBackend(cardData) {
+    try {
+      const result = await businessCardStore.saveCard(cardData);
+
+      if (result.success) {
+        console.log('名片保存成功');
+        // 重新加载数据
+        await this.loadMyCard();
+        return result;
+      } else {
+        console.error('保存名片失败:', result.message);
+        return result;
+      }
+    } catch (error) {
+      console.error('保存名片异常:', error);
+      return { success: false, message: error.message };
+    }
+  },
+
+  /**
+   * 删除名片
+   */
+  async deleteCard(cardId) {
+    try {
+      const result = await businessCardStore.deleteCard(cardId);
+
+      if (result.success) {
+        console.log('名片删除成功');
+        // 重新加载数据
+        await this.loadRealData();
+        return result;
+      } else {
+        console.error('删除名片失败:', result.message);
+        return result;
+      }
+    } catch (error) {
+      console.error('删除名片异常:', error);
+      return { success: false, message: error.message };
+    }
+  },
+
+  /**
+   * 获取名片详情
+   */
+  async getCardDetail(cardId) {
+    try {
+      const result = await businessCardStore.getCardDetail(cardId);
+
+      if (result.success) {
+        console.log('获取名片详情成功:', result.data);
+        return result;
+      } else {
+        console.error('获取名片详情失败:', result.message);
+        return result;
+      }
+    } catch (error) {
+      console.error('获取名片详情异常:', error);
+      return { success: false, message: error.message };
+    }
+  },
+
+  /**
+   * 处理隐藏名片事件
+   */
+  onHideCard(e) {
+    const { index, cardId } = e.detail;
+    console.log('隐藏名片:', { index, cardId });
+
+    // 从列表中移除该卡片
+    const userCards = [...this.data.userCards];
+    userCards.splice(index, 1);
+    this.setData({
+      userCards: userCards
+    });
+  },
+
+  /**
+   * 处理删除名片事件
+   */
+  async onDeleteCard(e) {
+    const { index, cardId } = e.detail;
+    console.log('删除名片:', { index, cardId });
+
+    // 立即从列表中移除该卡片，提供更好的用户体验
+    const userCards = [...this.data.userCards];
+    userCards.splice(index, 1);
+    this.setData({
+      userCards: userCards
+    });
+
+    // 重新加载我的名片数据以确保数据同步
+    await this.loadMyCard();
+  },
+
+  /**
+   * 刷新名片数据
+   */
+  async refreshCardList() {
+    await this.loadMyCard();
+  },
+
+  /**
+   * 下拉刷新
+   */
+  async onPullDownRefresh() {
+    await this.loadRealData();
+    wx.stopPullDownRefresh();
   }
 });
Index: ../weapp/app.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// app.js\r\nconst { checkLoginStatus, refreshToken } = require('./utils/auth');\r\nconst { handleLoginExpired } = require('./utils/loginHandler');\r\nconst { getStore, setStore } = require('./utils/util');\r\nconst { request } = require('./utils/request');\r\nconst apiManager = require('./services/apiManager');\r\n\r\nApp({\r\n  globalData: {\r\n    userInfo: null,\r\n    baseUrl: 'http://**************/api', // 添加基础API地址\r\n    refreshLock: false,\r\n    refreshInterval: null,\r\n    tokenTime: 2592000, // token 有效期（秒），可根据实际情况调整\r\n    request: request, // 添加全局request方法\r\n    api: apiManager, // 添加API管理器\r\n    isLoginExpiredHandling: false, // 防止重复处理登录过期\r\n  },\r\n  \r\n  onLaunch() {\r\n    // 初始化云开发环境\r\n    if (!wx.cloud) {\r\n      console.error('请使用 2.2.3 或以上的基础库以使用云能力');\r\n    } else {\r\n      wx.cloud.init({\r\n        env: 'cloud1-0gjj2zyo3acba1ed', // 你的云开发环境ID\r\n        traceUser: true\r\n      });\r\n    }\r\n\r\n    // 获取用户地理位置信息并保存到缓存\r\n    wx.getLocation({\r\n      type: 'wgs84',\r\n      success(res) {\r\n        const location = {\r\n          latitude: res.latitude,\r\n          longitude: res.longitude\r\n        };\r\n        setStore('userLocation', location);\r\n        console.log('已保存用户位置:', location);\r\n      },\r\n      fail(err) {\r\n        console.error('获取位置失败:', err);\r\n        setStore('userLocation', {\r\n          latitude: 0,\r\n          longitude: 0\r\n        });\r\n      }\r\n    });\r\n    \r\n    // 启动token自动刷新\r\n    this.startTokenRefresh();\r\n    \r\n    // 监听网络状态变化\r\n    this.setupNetworkListener();\r\n  },\r\n  \r\n  // 启动token自动刷新\r\n  startTokenRefresh() {\r\n    // 清除之前的定时器\r\n    if (this.globalData.refreshInterval) {\r\n      clearInterval(this.globalData.refreshInterval);\r\n    }\r\n    \r\n    this.globalData.refreshInterval = setInterval(() => {\r\n      const token = getStore('token') || {};\r\n      const refresh_token = getStore('refreshToken');\r\n      \r\n      if (!refresh_token || !token.value) {\r\n        return;\r\n      }\r\n      \r\n      const now = Date.now();\r\n      const tokenTime = this.globalData.tokenTime * 1000;\r\n      \r\n      // 检查token是否即将过期（提前5分钟刷新）\r\n      if (now - token.datetime >= (tokenTime - 5 * 60 * 1000) && !this.globalData.refreshLock) {\r\n        this.globalData.refreshLock = true;\r\n        console.log('开始自动刷新token...');\r\n        \r\n        refreshToken(refresh_token)\r\n          .then(res => {\r\n            console.log('token自动刷新成功');\r\n            setStore('token', {\r\n              value: res.data.accessToken,\r\n              datetime: Date.now()\r\n            });\r\n            setStore('refresh_token', res.data.refreshToken);\r\n            this.globalData.refreshLock = false;\r\n          })\r\n          .catch(err => {\r\n            console.error('自动刷新token失败', err);\r\n            this.globalData.refreshLock = false;\r\n            \r\n            // 如果刷新失败，可能是登录过期，触发登录过期处理\r\n            if (!this.globalData.isLoginExpiredHandling) {\r\n              this.globalData.isLoginExpiredHandling = true;\r\n              handleLoginExpired().finally(() => {\r\n                this.globalData.isLoginExpiredHandling = false;\r\n              });\r\n            }\r\n          });\r\n      }\r\n    }, 1000 * 60 * 5); // 每5分钟检查一次\r\n  },\r\n  \r\n  // 设置网络状态监听\r\n  setupNetworkListener() {\r\n    wx.onNetworkStatusChange((res) => {\r\n      console.log('网络状态变化:', res);\r\n      if (res.isConnected) {\r\n        // 网络恢复时，检查登录状态\r\n        if (checkLoginStatus()) {\r\n          console.log('网络恢复，检查登录状态正常');\r\n        } else {\r\n          console.log('网络恢复，但登录状态异常');\r\n        }\r\n      } else {\r\n        console.log('网络断开');\r\n      }\r\n    });\r\n  },\r\n  \r\n  // 全局错误处理\r\n  onError(error) {\r\n    console.error('小程序全局错误:', error);\r\n  },\r\n  \r\n  // 小程序显示时\r\n  onShow() {\r\n    // 检查登录状态\r\n    if (checkLoginStatus()) {\r\n      console.log('小程序显示，登录状态正常');\r\n    } else {\r\n      console.log('小程序显示，未登录状态');\r\n    }\r\n  },\r\n  \r\n  // 小程序隐藏时\r\n  onHide() {\r\n    console.log('小程序隐藏');\r\n  }\r\n});\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/app.js b/../weapp/app.js
--- a/../weapp/app.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/app.js	(date 1753950770738)
@@ -8,7 +8,7 @@
 App({
   globalData: {
     userInfo: null,
-    baseUrl: 'http://**************/api', // 添加基础API地址
+    baseUrl: 'http://localhost/api', // 添加基础API地址
     refreshLock: false,
     refreshInterval: null,
     tokenTime: 2592000, // token 有效期（秒），可根据实际情况调整
Index: ../weapp/config/api.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/**\r\n * API配置文件\r\n */\r\nconst config = {\r\n  // API基础地址\r\n  baseUrl: 'http://**************',\r\n  \r\n  // 文件上传相关接口\r\n  fileUpload: {\r\n    // 单文件上传\r\n    upload: '/blade-system/file-upload/upload',\r\n    // 批量文件上传\r\n    uploadBatch: '/blade-system/file-upload/upload-batch',\r\n    // 获取文件URL\r\n    getFileUrl: '/blade-system/file-upload/url',\r\n    // 删除文件\r\n    removeFile: '/blade-system/file-upload/remove',\r\n    // 获取文件列表\r\n    getFileList: '/blade-system/file-upload/list',\r\n    // 根据业务获取文件\r\n    getFilesByBusiness: '/blade-system/file-upload/business',\r\n    // 获取文件统计\r\n    getFileStats: '/blade-system/file-upload/stats',\r\n    // 清理过期文件\r\n    cleanExpiredFiles: '/blade-system/file-upload/clean-expired',\r\n    // 存储配置\r\n    storageConfig: '/blade-system/file-upload/storage-config'\r\n  },\r\n  \r\n  // 评论相关接口\r\n  comment: {\r\n    // 添加评论\r\n    add: '/blade-chat/feedback/comment/add',\r\n    // 回复评论\r\n    reply: '/blade-chat/feedback/comment/reply',\r\n    // 获取评论列表\r\n    list: '/blade-chat/feedback/comment/list',\r\n    // 获取回复列表\r\n    replies: '/blade-chat/feedback/comment/replies',\r\n    // 删除评论\r\n    remove: '/blade-chat/feedback/comment/remove',\r\n    // 点赞评论\r\n    like: '/blade-chat/feedback/comment/like',\r\n    // 获取评论详情\r\n    detail: '/blade-chat/feedback/comment/detail'\r\n  },\r\n\r\n  // 反馈相关接口\r\n  feedback: {\r\n    // 提交反馈\r\n    submit: '/blade-chat/feedback/submit',\r\n    // 获取反馈列表（帖子详情页面使用）\r\n    page: '/blade-chat/feedback/page',\r\n    // 获取反馈列表（通用）\r\n    list: '/blade-chat/feedback/list',\r\n    // 标记有帮助\r\n    helpful: '/blade-chat/feedback/helpful',\r\n    // 获取反馈标签\r\n    tags: '/blade-chat/feedback/getTagsByCategory',\r\n    // 获取热门标签\r\n    hotTags: '/blade-chat/feedback/getHotTags'\r\n  },\r\n\r\n  // 帖子相关接口\r\n  post: {\r\n    // 帖子详情\r\n    detail: '/blade-chat/post/detail',\r\n    // 点赞帖子\r\n    like: '/blade-chat/post/like',\r\n    // 收藏帖子\r\n    favorite: '/blade-chat/post/favorite',\r\n    // 获取帖子列表\r\n    list: '/blade-chat/post/list',\r\n    // 发布帖子\r\n    publish: '/blade-chat/post/publish'\r\n  },\r\n\r\n  // 签到相关接口\r\n  signin: {\r\n    // 获取签到信息\r\n    info: '/blade-chat/signin/info',\r\n    // 执行签到\r\n    doSignin: '/blade-chat/signin/do',\r\n    // 获取月度签到记录\r\n    monthRecord: '/blade-chat/signin/record',\r\n    // 获取签到统计\r\n    stats: '/blade-chat/signin/stats',\r\n    // 获取签到记录列表（分页）\r\n    records: '/blade-chat/signin/records',\r\n    // 获取签到统计汇总\r\n    summary: '/blade-chat/signin/summary',\r\n    // 查询用户是否中奖\r\n    queryUserWin: '/blade-chat/signin/queryUserWin'\r\n  },\r\n\r\n  // 用户相关接口\r\n  user: {\r\n    // 获取用户信息\r\n    info: '/blade-chat/user/info',\r\n    // 更新用户信息\r\n    update: '/blade-chat/user/update',\r\n    // 获取用户统计\r\n    stats: '/blade-chat/user/stats',\r\n    // 获取用户积分\r\n    points: '/blade-chat/user/points'\r\n  },\r\n\r\n  // 名片相关接口\r\n  businessCard: {\r\n    // 获取当前用户名片详情\r\n    myCard: '/blade-chat/user/card/my-card',\r\n    // 获取名片详情\r\n    detail: '/blade-chat/user/card/detail',\r\n    // 获取名片列表\r\n    page: '/blade-chat/user/card/page',\r\n    // 保存名片\r\n    save: '/blade-chat/user/card/save',\r\n    // 更新名片\r\n    update: '/blade-chat/user/card/update',\r\n    // 提交名片（新增或修改）\r\n    submit: '/blade-chat/user/card/submit',\r\n    // 删除名片\r\n    remove: '/blade-chat/user/card/remove'\r\n  },\r\n\r\n  // 机构相关接口\r\n  institution: {\r\n    // 获取机构列表（分页）\r\n    page: '/blade-chat-open/institution/page',    // 获取机构详情\r\n    detail: '/blade-chat/institution/detail',\r\n    // 获取我加入的机构列表\r\n    myInstitutions: '/blade-chat/institution/my-institutions',\r\n    // 申请加入机构\r\n    apply: '/blade-chat/institution/apply',\r\n    // 创建机构\r\n    create: '/blade-chat/institution/create',\r\n    // 更新机构信息\r\n    update: '/blade-chat/institution/update',\r\n    // 删除机构\r\n    remove: '/blade-chat/institution/remove'\r\n  },\r\n\r\n  // 机构分类相关接口\r\n  institutionType: {\r\n    // 获取机构分类列表\r\n    list: '/blade-chat-open/institution/type/list',\r\n    // 获取机构分类详情\r\n    detail: '/blade-chat-open/institution-type/detail'\r\n  },\r\n\r\n  // 认证相关\r\n  auth: {\r\n    tenantId: '000000',\r\n    basicAuth: 'Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U='\r\n  },\r\n  \r\n  // 文件上传配置\r\n  upload: {\r\n    // 最大文件大小（字节）\r\n    maxFileSize: 10 * 1024 * 1024, // 10MB\r\n    // 支持的文件类型\r\n    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\r\n    // 最大图片数量\r\n    maxImageCount: 6,\r\n    // 上传来源\r\n    uploadSource: 'miniapp'\r\n  }\r\n};\r\n\r\nmodule.exports = config; 
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/config/api.js b/../weapp/config/api.js
--- a/../weapp/config/api.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/config/api.js	(date *************)
@@ -3,7 +3,7 @@
  */
 const config = {
   // API基础地址
-  baseUrl: 'http://**************',
+  baseUrl: 'http://localhost',
   
   // 文件上传相关接口
   fileUpload: {
Index: ../weapp/pkg_user/utils/request.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址\r\nconst BASE_URL = 'http://**************'; // 替换为您的后端API地址\r\n\r\nconst { handleLoginExpired } = require('../../utils/loginHandler');\r\n\r\n// 请求拦截器\r\nconst requestInterceptor = (config) => {\r\n  const token = wx.getStorageSync('token').value;\r\n  const openId = wx.getStorageSync('openId');\r\n\r\n  config.header = {\r\n    ...config.header,\r\n    'Tenant-Id' :\"000000\",\r\n    'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,\r\n  };\r\n\r\n  if (token) {\r\n    config.header = {\r\n      ...config.header,\r\n      'Blade-Auth': `Bearer ${token}`\r\n    };\r\n  }\r\n\r\n  // 添加OpenID头\r\n  if (openId) {\r\n    config.header = {\r\n      ...config.header,\r\n      'X-Open-ID': openId\r\n    };\r\n  }\r\n  \r\n  return config;\r\n};\r\n\r\n// 响应拦截器\r\nconst responseInterceptor = (response) => {\r\n  const { data } = response;\r\n  // 处理token过期\r\n  if (data.code === 401) {\r\n    handleLoginExpired();\r\n    return Promise.reject(new Error('登录已过期，请重新登录'));\r\n  }\r\n  \r\n  return data;\r\n};\r\n\r\n// 刷新token\r\nconst refreshToken = () => {\r\n  return new Promise((resolve, reject) => {\r\n    const refreshToken = wx.getStorageSync('refreshToken');\r\n    if (!refreshToken) {\r\n      reject(new Error('未找到刷新token'));\r\n      return;\r\n    }\r\n\r\n    wx.request({\r\n      url: `${BASE_URL}/blade-auth/token`,\r\n      method: 'POST',\r\n      data: {\r\n        grantType: 'refresh_token',\r\n        refreshToken: refreshToken\r\n      },\r\n      success: (res) => {\r\n        if (res.data.success) {\r\n          const { accessToken, refreshToken } = res.data.data;\r\n          wx.setStorageSync('token', accessToken);\r\n          wx.setStorageSync('refreshToken', refreshToken);\r\n          resolve(accessToken);\r\n        } else {\r\n          reject(new Error(res.data.msg));\r\n        }\r\n      },\r\n      fail: reject\r\n    });\r\n  });\r\n};\r\n\r\n// 统一请求方法\r\nconst request = (options) => {\r\n  const config = requestInterceptor(options);\r\n  console.log(config);  \r\n  // accessToken: \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aM2NpDQsCGbjX-KXssWkyHv4S6WRWhKwBM3rpuy3pOk\"\r\n\r\n  return new Promise((resolve, reject) => {\r\n    wx.request({\r\n      ...config,\r\n      url: `${BASE_URL}${config.url}`,\r\n        success: (res) => {\r\n        resolve(responseInterceptor(res));\r\n      },\r\n      fail: (err) => {\r\n        reject(err);\r\n      }\r\n    });\r\n  });\r\n};\r\n\r\nmodule.exports = {\r\n  request,\r\n  refreshToken\r\n}; 
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/utils/request.js b/../weapp/pkg_user/utils/request.js
--- a/../weapp/pkg_user/utils/request.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/utils/request.js	(date *************)
@@ -1,5 +1,5 @@
 // const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址
-const BASE_URL = 'http://**************'; // 替换为您的后端API地址
+const BASE_URL = 'http://localhost'; // 替换为您的后端API地址
 
 const { handleLoginExpired } = require('../../utils/loginHandler');
 
Index: ../weapp/docs/用户名片页面后端接口接入指南.md
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/docs/用户名片页面后端接口接入指南.md b/../weapp/docs/用户名片页面后端接口接入指南.md
new file mode 100644
--- /dev/null	(date 1753950986583)
+++ b/../weapp/docs/用户名片页面后端接口接入指南.md	(date 1753950986583)
@@ -0,0 +1,334 @@
+# 用户名片页面后端接口接入指南
+
+## 概述
+
+本文档介绍了如何在保持现有UI不变的情况下，为用户名片页面接入后端接口，实现真实数据的获取和管理。
+
+## 实现策略
+
+### 🎯 **核心思路**
+- **保持UI不变**：不修改现有的WXML和WXSS文件
+- **后台数据加载**：在页面加载时静默获取真实数据
+- **数据格式转换**：将后端数据转换为前端展示格式
+- **渐进式增强**：逐步用真实数据替换模拟数据
+
+### 📡 **接口集成**
+- **名片管理**：获取、保存、删除个人名片
+- **帖子查询**：分页查询用户发布的帖子
+- **数据同步**：实时同步后端数据到前端展示
+
+## 后端接口
+
+### 🔧 **控制器增强**
+
+在 `WeChatBusinessCardController.java` 中新增了以下接口：
+
+```java
+/**
+ * 获取当前用户名片
+ */
+@GetMapping("/my-card")
+public R<BusinessCardVO> getMyCard()
+
+/**
+ * 获取我的帖子列表
+ */
+@GetMapping("/my-posts")
+public R<IPage<SupPostVO>> getMyPosts(Query query)
+
+/**
+ * 分页查询名片列表
+ */
+@GetMapping("/list")
+public R<IPage<BusinessCardVO>> getCardList(BusinessCardVO businessCard, Query query)
+
+/**
+ * 获取名片详情
+ */
+@GetMapping("/detail")
+public R<BusinessCardVO> getCardDetail(@RequestParam Long id)
+```
+
+### 📊 **数据结构映射**
+
+**后端实体 → 前端展示格式：**
+```javascript
+// 后端 BusinessCard 字段 → 前端 userCards 字段
+{
+  id → cardId,
+  company → company,
+  jobTitle → position,
+  businessProfile → businessIntro,
+  fullName → name,
+  gender → gender,
+  phone → phone,
+  address → address,
+  email → email,
+  website → website,
+  weixin → wechat,
+  avatar → avatar,
+  images → images (split by comma),
+  video → videos (array),
+  description → remark,
+  isPublic → isPublic,
+  auditStatus → auditStatus
+}
+```
+
+## 前端实现
+
+### 📱 **页面逻辑增强**
+
+#### 1. 数据初始化
+```javascript
+Page({
+  data: {
+    // 后端数据
+    myCard: null,
+    myPosts: [],
+    loading: false,
+    
+    // 原有的模拟数据（保持UI展示）
+    userCards: [/* 原有数据 */]
+  },
+
+  onLoad() {
+    console.log('用户卡片页面加载', this.data.userCards);
+    // 后台加载真实数据
+    this.loadRealData();
+  }
+});
+```
+
+#### 2. 数据加载方法
+```javascript
+/**
+ * 加载真实数据（后台接口）
+ */
+async loadRealData() {
+  this.setData({ loading: true });
+  
+  try {
+    // 并行加载我的名片和我的帖子
+    const [cardResult, postsResult] = await Promise.all([
+      this.loadMyCard(),
+      this.loadMyPosts()
+    ]);
+
+    console.log('我的名片:', cardResult);
+    console.log('我的帖子:', postsResult);
+    
+  } catch (error) {
+    console.error('加载数据失败:', error);
+  } finally {
+    this.setData({ loading: false });
+  }
+}
+```
+
+#### 3. 名片数据处理
+```javascript
+/**
+ * 加载我的名片
+ */
+async loadMyCard() {
+  try {
+    const result = await businessCardStore.getMyCard();
+    
+    if (result.success && result.data) {
+      this.setData({ myCard: result.data });
+      
+      // 如果有真实名片数据，更新第一个模拟数据用于展示
+      if (result.data.id) {
+        const realCard = this.convertToDisplayFormat(result.data);
+        const userCards = [realCard, ...this.data.userCards.slice(1)];
+        this.setData({ userCards });
+      }
+    }
+    
+    return result;
+  } catch (error) {
+    console.error('加载名片异常:', error);
+    return { success: false, message: error.message };
+  }
+}
+```
+
+#### 4. 数据格式转换
+```javascript
+/**
+ * 转换后端数据格式为前端展示格式
+ */
+convertToDisplayFormat(backendCard) {
+  return {
+    cardId: backendCard.id,
+    company: backendCard.company || '未填写公司',
+    position: backendCard.jobTitle || '未填写职位',
+    businessIntro: backendCard.businessProfile || '未填写业务简介',
+    name: backendCard.fullName || '未填写姓名',
+    gender: backendCard.gender || 0,
+    phone: backendCard.phone || '未填写电话',
+    address: backendCard.address || '未填写地址',
+    email: backendCard.email || '未填写邮箱',
+    website: backendCard.website || '未填写网址',
+    wechat: backendCard.weixin || '未填写微信',
+    avatar: backendCard.avatar || '/assets/images/def-avatar.png',
+    logo: '/assets/images/bot-avatar.png',
+    qrCode: '/assets/images/bot-avatar.png',
+    images: backendCard.images ? backendCard.images.split(',') : [],
+    videos: backendCard.video ? [backendCard.video] : [],
+    remark: backendCard.description || '暂无备注',
+    isPublic: backendCard.isPublic || 0,
+    updateTime: backendCard.updateTime || new Date().toLocaleString(),
+    auditStatus: backendCard.auditStatus || 0,
+    cardType: '个人名片'
+  };
+}
+```
+
+### 🔄 **Store 管理**
+
+使用现有的 `businessCardStore.js`，主要方法：
+
+```javascript
+// 获取我的名片
+await businessCardStore.getMyCard()
+
+// 获取我的帖子
+await businessCardStore.getMyPosts({ current: 1, size: 10 })
+
+// 保存名片
+await businessCardStore.saveCard(cardData)
+
+// 删除名片
+await businessCardStore.deleteCard(ids)
+
+// 获取名片详情
+await businessCardStore.getCardDetail(id)
+```
+
+## 功能特性
+
+### ✅ **已实现功能**
+
+1. **数据获取**
+   - ✅ 获取当前用户名片信息
+   - ✅ 分页查询用户发布的帖子
+   - ✅ 并行加载提高性能
+
+2. **数据管理**
+   - ✅ 保存/更新名片信息
+   - ✅ 删除名片功能
+   - ✅ 获取名片详情
+
+3. **用户体验**
+   - ✅ 保持原有UI展示效果
+   - ✅ 后台静默加载真实数据
+   - ✅ 支持下拉刷新
+
+4. **数据同步**
+   - ✅ 真实数据替换模拟数据
+   - ✅ 格式转换确保兼容性
+   - ✅ 错误处理和日志记录
+
+### 🔄 **数据流程**
+
+```
+页面加载 → 显示模拟数据 → 后台加载真实数据 → 转换数据格式 → 更新展示数据
+```
+
+### 📝 **使用示例**
+
+#### 1. 获取我的名片和帖子
+```javascript
+// 页面加载时自动调用
+onLoad() {
+  this.loadRealData(); // 后台加载真实数据
+}
+
+// 下拉刷新时调用
+async onPullDownRefresh() {
+  await this.loadRealData();
+  wx.stopPullDownRefresh();
+}
+```
+
+#### 2. 保存名片
+```javascript
+// 调用保存方法
+const result = await this.saveCardToBackend({
+  company: '公司名称',
+  jobTitle: '职位',
+  fullName: '姓名',
+  // ... 其他字段
+});
+
+if (result.success) {
+  console.log('保存成功');
+}
+```
+
+#### 3. 删除名片
+```javascript
+// 调用删除方法
+const result = await this.deleteCard(cardId);
+
+if (result.success) {
+  console.log('删除成功');
+}
+```
+
+## 技术要点
+
+### 🔧 **关键实现**
+
+1. **无侵入式集成**
+   - 保持原有UI组件不变
+   - 在后台静默加载真实数据
+   - 通过数据转换确保兼容性
+
+2. **数据格式兼容**
+   - 后端字段映射到前端字段
+   - 处理空值和默认值
+   - 数组和字符串的转换
+
+3. **性能优化**
+   - 并行加载多个接口
+   - 避免重复请求
+   - 合理的错误处理
+
+4. **用户体验**
+   - 页面立即显示模拟数据
+   - 后台加载不影响用户操作
+   - 数据更新时平滑过渡
+
+## 测试验证
+
+### 🧪 **测试要点**
+
+1. **数据加载测试**
+   - [ ] 页面加载时正确获取名片数据
+   - [ ] 帖子列表正确显示
+   - [ ] 数据格式转换正确
+
+2. **功能测试**
+   - [ ] 保存名片功能正常
+   - [ ] 删除名片功能正常
+   - [ ] 下拉刷新功能正常
+
+3. **兼容性测试**
+   - [ ] 原有UI展示正常
+   - [ ] 模拟数据和真实数据切换无异常
+   - [ ] 组件交互功能正常
+
+## 总结
+
+通过这种无侵入式的接口接入方式，我们成功实现了：
+
+- ✅ **保持UI稳定**：原有界面和交互完全不变
+- ✅ **数据真实化**：后台获取真实的名片和帖子数据
+- ✅ **功能完整性**：支持名片的增删改查操作
+- ✅ **开发规范**：严格遵循Store模式和组件化设计
+- ✅ **用户体验**：无感知的数据加载和更新
+
+这种方式既保证了现有功能的稳定性，又为后续的功能扩展提供了良好的基础。
Index: ../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxss
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>.card-list-container {\r\n  width: 100%;\r\n  padding: 24rpx;\r\n  background: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.card-list-column {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 32rpx;\r\n}\r\n\r\n.business-card {\r\n  width: 100%;\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.card-content {\r\n  padding: 32rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 24rpx;\r\n}\r\n\r\n/* 添加名片按钮 */\r\n.add-card-btn {\r\n  width: 100%;\r\n  background: #fff;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 2rpx solid transparent;\r\n}\r\n\r\n.add-card-btn:active {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);\r\n  border-color: #ff8080;\r\n}\r\n\r\n.add-btn-content {\r\n  padding: 40rpx 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n  position: relative;\r\n}\r\n\r\n.add-icon {\r\n  font-size: 48rpx;\r\n  color: #ff8080;\r\n  font-weight: bold;\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  text-align: center;\r\n  line-height: 55rpx;\r\n}\r\n\r\n.add-text {\r\n  font-size: 34rpx;\r\n  color: #333;\r\n  font-weight: 600;\r\n  flex: 1;\r\n}\r\n\r\n/* 卡片头部 */\r\n.card-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 20rpx;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.avatar-section {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.avatar {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  background: #f0f0f0;\r\n}\r\n\r\n.avatar-placeholder {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  background: #e0e0e0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-text {\r\n  font-size: 32rpx;\r\n  color: #666;\r\n  font-weight: 500;\r\n}\r\n\r\n.company-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8rpx;\r\n}\r\n\r\n.company-name {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  line-height: 1.4;\r\n}\r\n\r\n.person-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.person-name {\r\n  font-size: 28rpx;\r\n  color: #ff8080;\r\n  font-weight: 600;\r\n}\r\n\r\n.person-position {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.status-tag {\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 12rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-tag.approved {\r\n  background: #e8f5e8;\r\n  color: #52c41a;\r\n}\r\n\r\n.status-tag.pending {\r\n  background: #fff7e6;\r\n  color: #fa8c16;\r\n}\r\n\r\n.status-tag.rejected {\r\n  background: #fff1f0;\r\n  color: #f5222d;\r\n}\r\n\r\n/* 联系信息 */\r\n.contact-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12rpx;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  min-height: 32rpx;\r\n}\r\n\r\n.info-icon {\r\n  font-size: 24rpx;\r\n  flex-shrink: 0;\r\n  width: 24rpx;\r\n  text-align: center;\r\n}\r\n\r\n.info-text {\r\n  font-size: 26rpx;\r\n  color: #555;\r\n  line-height: 1.4;\r\n  flex: 1;\r\n}\r\n\r\n/* 卡片底部 */\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-top: 20rpx;\r\n  border-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.footer-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.time-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6rpx;\r\n}\r\n\r\n.time-icon {\r\n  font-size: 20rpx;\r\n  width: 20rpx;\r\n  text-align: center;\r\n}\r\n\r\n.time-text {\r\n  font-size: 22rpx;\r\n  color: #999;\r\n}\r\n\r\n.card-type-tag {\r\n  padding: 4rpx 12rpx;\r\n  background: #f5f5f5;\r\n  border-radius: 12rpx;\r\n  font-size: 20rpx;\r\n  color: #666;\r\n}\r\n\r\n.footer-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.action-btn {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.action-icon {\r\n  font-size: 24rpx;\r\n  width: 24rpx;\r\n  text-align: center;\r\n}\r\n\r\n/* 无数据状态 */\r\n.no-cards {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 400rpx;\r\n  color: #999;\r\n  font-size: 28rpx;\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxss b/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxss
--- a/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxss	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxss	(date 1753954133685)
@@ -45,6 +45,35 @@
   border-color: #ff8080;
 }
 
+/* 空状态样式 */
+.empty-state {
+  display: flex;
+  flex-direction: column;
+  align-items: center;
+  justify-content: center;
+  padding: 120rpx 40rpx;
+  text-align: center;
+}
+
+.empty-icon {
+  font-size: 120rpx;
+  margin-bottom: 32rpx;
+  opacity: 0.6;
+}
+
+.empty-text {
+  font-size: 32rpx;
+  color: #666;
+  margin-bottom: 16rpx;
+  font-weight: 500;
+}
+
+.empty-desc {
+  font-size: 28rpx;
+  color: #999;
+  line-height: 1.5;
+}
+
 .add-btn-content {
   padding: 40rpx 32rpx;
   display: flex;
Index: ../weapp/pkg_merchant/stores/merchantStore.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>const { request } = require('../utils/request');\r\n\r\n// 获取验证码\r\nconst getVerifyCode = (phone) => {\r\n  return request({\r\n    url: '/api/merchant/verify-code',\r\n    method: 'POST',\r\n    data: { phone }\r\n  });\r\n};\r\n\r\n// 上传图片（通用）\r\nconst uploadImage = (filePath) => {\r\n  return new Promise((resolve, reject) => {\r\n    wx.uploadFile({\r\n      url: 'http://**************/api/merchant/upload', // 替换为实际上传接口\r\n      filePath,\r\n      name: 'file',\r\n      success: res => {\r\n        const data = JSON.parse(res.data);\r\n        if (data.code === 200) {\r\n          resolve(data.data.url);\r\n        } else {\r\n          reject(data.msg);\r\n        }\r\n      },\r\n      fail: reject\r\n    });\r\n  });\r\n};\r\n\r\n// 提交商家入驻表单\r\nconst submitMerchant = (form) => {\r\n  return request({\r\n    url: '/api/merchant/settle',\r\n    method: 'POST',\r\n    data: form\r\n  });\r\n};\r\n\r\nmodule.exports = {\r\n  getVerifyCode,\r\n  uploadImage,\r\n  submitMerchant\r\n}; 
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_merchant/stores/merchantStore.js b/../weapp/pkg_merchant/stores/merchantStore.js
--- a/../weapp/pkg_merchant/stores/merchantStore.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_merchant/stores/merchantStore.js	(date *************)
@@ -13,7 +13,7 @@
 const uploadImage = (filePath) => {
   return new Promise((resolve, reject) => {
     wx.uploadFile({
-      url: 'http://**************/api/merchant/upload', // 替换为实际上传接口
+      url: 'http://localhost/api/merchant/upload', // 替换为实际上传接口
       filePath,
       name: 'file',
       success: res => {
Index: ../weapp/stores/businessCardStore.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/**\r\n * 名片管理 Store\r\n */\r\nconst { request } = require('../utils/request.js');\r\nconst api = require('../config/api.js');\r\n\r\nclass BusinessCardStore {\r\n  constructor() {\r\n    this.myCard = null;\r\n    this.loading = false;\r\n    this.error = null;\r\n  }\r\n\r\n  /**\r\n   * 获取当前用户名片\r\n   */\r\n  async getMyCard() {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    try {\r\n      const response = await request({\r\n        url: api.businessCard.myCard,\r\n        method: 'GET'\r\n      });\r\n\r\n      if (response.success) {\r\n        this.myCard = response.data || {};\r\n        return {\r\n          success: true,\r\n          data: this.myCard\r\n        };\r\n      } else {\r\n        this.error = response.msg || '获取名片失败';\r\n        return {\r\n          success: false,\r\n          message: this.error\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('获取名片失败:', error);\r\n      this.error = error.message || '网络请求失败';\r\n      return {\r\n        success: false,\r\n        message: this.error\r\n      };\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 保存或更新名片\r\n   * @param {Object} cardData - 名片数据\r\n   */\r\n  async saveCard(cardData) {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    try {\r\n      const response = await request({\r\n        url: api.businessCard.submit,\r\n        method: 'POST',\r\n        data: cardData\r\n      });\r\n\r\n      if (response.success) {\r\n        // 更新本地缓存\r\n        this.myCard = { ...this.myCard, ...cardData };\r\n        return {\r\n          success: true,\r\n          message: '保存成功'\r\n        };\r\n      } else {\r\n        this.error = response.msg || '保存失败';\r\n        return {\r\n          success: false,\r\n          message: this.error\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('保存名片失败:', error);\r\n      this.error = error.message || '网络请求失败';\r\n      return {\r\n        success: false,\r\n        message: this.error\r\n      };\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取名片详情（通过ID）\r\n   * @param {String} cardId - 名片ID\r\n   */\r\n  async getCardDetail(cardId) {\r\n    try {\r\n      const response = await request({\r\n        url: api.businessCard.detail,\r\n        method: 'GET',\r\n        data: { id: cardId }\r\n      });\r\n\r\n      if (response.success) {\r\n        return {\r\n          success: true,\r\n          data: response.data\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: response.msg || '获取名片详情失败'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('获取名片详情失败:', error);\r\n      return {\r\n        success: false,\r\n        message: error.message || '网络请求失败'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取名片列表（分页）\r\n   * @param {Object} params - 查询参数\r\n   */\r\n  async getCardList(params = {}) {\r\n    try {\r\n      const response = await request({\r\n        url: api.businessCard.page,\r\n        method: 'GET',\r\n        data: params\r\n      });\r\n\r\n      if (response.success) {\r\n        return {\r\n          success: true,\r\n          data: response.data\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: response.msg || '获取名片列表失败'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('获取名片列表失败:', error);\r\n      return {\r\n        success: false,\r\n        message: error.message || '网络请求失败'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 验证名片数据\r\n   * @param {Object} cardData - 名片数据\r\n   */\r\n  validateCard(cardData) {\r\n    const errors = [];\r\n\r\n    // 必填字段验证\r\n    if (!cardData.fullName || cardData.fullName.trim() === '') {\r\n      errors.push('姓名不能为空');\r\n    }\r\n\r\n    if (!cardData.phone || cardData.phone.trim() === '') {\r\n      errors.push('电话不能为空');\r\n    } else {\r\n      // 电话格式验证\r\n      const phoneRegex = /^1[3-9]\\d{9}$/;\r\n      if (!phoneRegex.test(cardData.phone)) {\r\n        errors.push('请输入正确的手机号码');\r\n      }\r\n    }\r\n\r\n    // 邮箱格式验证（如果填写了邮箱）\r\n    if (cardData.email && cardData.email.trim() !== '') {\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      if (!emailRegex.test(cardData.email)) {\r\n        errors.push('请输入正确的邮箱地址');\r\n      }\r\n    }\r\n\r\n    // 网址格式验证（如果填写了网址）\r\n    if (cardData.website && cardData.website.trim() !== '') {\r\n      const urlRegex = /^https?:\\/\\/.+/;\r\n      if (!urlRegex.test(cardData.website)) {\r\n        errors.push('请输入正确的网址（以http://或https://开头）');\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors: errors\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 格式化名片数据用于显示\r\n   * @param {Object} cardData - 原始名片数据\r\n   */\r\n  formatCardForDisplay(cardData) {\r\n    if (!cardData) return null;\r\n\r\n    return {\r\n      ...cardData,\r\n      genderText: this.getGenderText(cardData.gender),\r\n      isPublicText: cardData.isPublic === 1 ? '公开' : '私密',\r\n      hasAvatar: !!(cardData.avatar && cardData.avatar.trim()),\r\n      hasImages: !!(cardData.images && cardData.images.trim()),\r\n      hasVideo: !!(cardData.video && cardData.video.trim()),\r\n      imageList: cardData.images ? cardData.images.split(',').filter(img => img.trim()) : [],\r\n      displayPhone: this.formatPhone(cardData.phone),\r\n      displayEmail: cardData.email || '未填写',\r\n      displayAddress: cardData.address || '未填写',\r\n      displayWebsite: cardData.website || '未填写',\r\n      displayWeixin: cardData.weixin || '未填写'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 获取性别文本\r\n   * @param {Number} gender - 性别代码\r\n   */\r\n  getGenderText(gender) {\r\n    switch (gender) {\r\n      case 1: return '男';\r\n      case 2: return '女';\r\n      default: return '保密';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 格式化电话号码显示\r\n   * @param {String} phone - 电话号码\r\n   */\r\n  formatPhone(phone) {\r\n    if (!phone) return '';\r\n    // 手机号码中间4位用*替换\r\n    if (phone.length === 11) {\r\n      return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\r\n    }\r\n    return phone;\r\n  }\r\n\r\n  /**\r\n   * 获取状态\r\n   */\r\n  getState() {\r\n    return {\r\n      myCard: this.myCard,\r\n      loading: this.loading,\r\n      error: this.error\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 清理数据\r\n   */\r\n  clear() {\r\n    this.myCard = null;\r\n    this.loading = false;\r\n    this.error = null;\r\n  }\r\n}\r\n\r\n// 创建单例实例\r\nconst businessCardStore = new BusinessCardStore();\r\n\r\nmodule.exports = businessCardStore;\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/stores/businessCardStore.js b/../weapp/stores/businessCardStore.js
--- a/../weapp/stores/businessCardStore.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/stores/businessCardStore.js	(date 1753954210835)
@@ -12,7 +12,7 @@
   }
 
   /**
-   * 获取当前用户名片
+   * 获取当前用户名片列表
    */
   async getMyCard() {
     this.loading = true;
@@ -20,12 +20,13 @@
 
     try {
       const response = await request({
-        url: api.businessCard.myCard,
+        url: '/blade-chat/card/my-card',
         method: 'GET'
       });
 
       if (response.success) {
-        this.myCard = response.data || {};
+        // 后端返回的是名片列表数组
+        this.myCard = response.data || [];
         return {
           success: true,
           data: this.myCard
@@ -49,6 +50,7 @@
     }
   }
 
+
   /**
    * 保存或更新名片
    * @param {Object} cardData - 名片数据
@@ -59,14 +61,31 @@
 
     try {
       const response = await request({
-        url: api.businessCard.submit,
+        url: '/blade-chat/card/submit',
         method: 'POST',
         data: cardData
       });
 
       if (response.success) {
         // 更新本地缓存
-        this.myCard = { ...this.myCard, ...cardData };
+        if (Array.isArray(this.myCard)) {
+          if (cardData.id) {
+            // 更新现有名片
+            const index = this.myCard.findIndex(card => card.id === cardData.id);
+            if (index !== -1) {
+              this.myCard[index] = { ...this.myCard[index], ...cardData };
+            } else {
+              // 如果没找到，添加新名片
+              this.myCard.push(cardData);
+            }
+          } else {
+            // 新增名片，添加到列表
+            this.myCard.push(cardData);
+          }
+        } else {
+          // 如果缓存不是数组，重新初始化
+          this.myCard = [cardData];
+        }
         return {
           success: true,
           message: '保存成功'
@@ -97,7 +116,7 @@
   async getCardDetail(cardId) {
     try {
       const response = await request({
-        url: api.businessCard.detail,
+        url: '/blade-chat/card/detail',
         method: 'GET',
         data: { id: cardId }
       });
@@ -129,7 +148,7 @@
   async getCardList(params = {}) {
     try {
       const response = await request({
-        url: api.businessCard.page,
+        url: '/blade-chat/card/list',
         method: 'GET',
         data: params
       });
@@ -154,6 +173,49 @@
     }
   }
 
+  /**
+   * 删除名片
+   * @param {String} ids - 名片ID列表，逗号分隔
+   */
+  async deleteCard(ids) {
+    this.loading = true;
+    this.error = null;
+
+    try {
+      // 后端接口需要通过URL参数传递ids
+      const response = await request({
+        url: `/blade-chat/card/remove?ids=${ids}`,
+        method: 'POST'
+      });
+
+      if (response.success) {
+        // 如果删除的是当前用户的名片，从本地缓存中移除
+        if (Array.isArray(this.myCard)) {
+          this.myCard = this.myCard.filter(card => !ids.includes(card.id.toString()));
+        }
+        return {
+          success: true,
+          message: '删除成功'
+        };
+      } else {
+        this.error = response.msg || '删除失败';
+        return {
+          success: false,
+          message: this.error
+        };
+      }
+    } catch (error) {
+      console.error('删除名片失败:', error);
+      this.error = error.message || '网络请求失败';
+      return {
+        success: false,
+        message: this.error
+      };
+    } finally {
+      this.loading = false;
+    }
+  }
+
   /**
    * 验证名片数据
    * @param {Object} cardData - 名片数据
Index: ../weapp/pkg_user/pages/user-card/user-card.wxml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><!--我的名片页面-->\r\n<view class=\"user-card-container\">\r\n  <!-- 多张名片横向滑动展示 -->\r\n  <user-card-list cardList=\"{{userCards}}\" />\r\n</view>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/pages/user-card/user-card.wxml b/../weapp/pkg_user/pages/user-card/user-card.wxml
--- a/../weapp/pkg_user/pages/user-card/user-card.wxml	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/pages/user-card/user-card.wxml	(date 1753954336735)
@@ -1,5 +1,10 @@
 <!--我的名片页面-->
-<view class="user-card-container">
-  <!-- 多张名片横向滑动展示 -->
-  <user-card-list cardList="{{userCards}}" />
+<view wx:if="{{loading}}" class="loading-container">
+  <text>加载中...</text>
 </view>
+<user-card-list
+  wx:else
+  cardList="{{userCards}}"
+  bind:hideCard="onHideCard"
+  bind:deleteCard="onDeleteCard"
+/>
Index: ../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><view wx:if=\"{{!cardList || cardList.length === 0}}\" class=\"no-cards\">\r\n  <text>暂无名片数据</text>\r\n</view>\r\n<view wx:else class=\"card-list-container\">\r\n  <view class=\"card-list-column\">\r\n    <block wx:for=\"{{cardList}}\" wx:key=\"cardId\">\r\n      <view class=\"business-card\">\r\n        <!-- 卡片主体 -->\r\n        <view class=\"card-content\">\r\n          <!-- 顶部：公司信息和头像 -->\r\n          <view class=\"card-header\">\r\n            <view class=\"avatar-section\">\r\n              <image wx:if=\"{{item.avatar}}\" class=\"avatar\" src=\"{{item.avatar}}\" mode=\"aspectFill\"/>\r\n              <view wx:else class=\"avatar-placeholder\">\r\n                <text class=\"avatar-text\">{{item.name ? item.name.charAt(0) : '觅'}}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"company-section\">\r\n              <text class=\"company-name\">{{item.company}}</text>\r\n              <view class=\"person-info\">\r\n                <text class=\"person-name\">{{item.name}}</text>\r\n                <text class=\"person-position\">{{item.position}}</text>\r\n                <view class=\"status-tag {{item.auditStatus === 1 ? 'approved' : item.auditStatus === 0 ? 'pending' : 'rejected'}}\">\r\n                  <text>{{item.auditStatus === 1 ? '已通过' : item.auditStatus === 0 ? '待审核' : '未通过'}}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 联系信息 -->\r\n          <view class=\"contact-info\">\r\n            <view wx:if=\"{{item.address}}\" class=\"info-item\">\r\n              <text class=\"info-icon\">\uD83D\uDCCD</text>\r\n              <text class=\"info-text\">{{item.address}}</text>\r\n            </view>\r\n            <view wx:if=\"{{item.phone}}\" class=\"info-item\">\r\n              <text class=\"info-icon\">\uD83D\uDCF1</text>\r\n              <text class=\"info-text\">{{item.phone}}</text>\r\n            </view>\r\n            <view wx:if=\"{{item.wechat}}\" class=\"info-item\">\r\n              <text class=\"info-icon\">\uD83D\uDCAC</text>\r\n              <text class=\"info-text\">{{item.wechat}}</text>\r\n            </view>\r\n            <view wx:if=\"{{item.businessIntro}}\" class=\"info-item\">\r\n              <text class=\"info-text\">{{item.businessIntro}}</text>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 底部：时间、类型和操作按钮 -->\r\n          <view class=\"card-footer\">\r\n            <view class=\"footer-left\">\r\n              <view class=\"time-info\">\r\n                <text class=\"time-icon\">\uD83D\uDD50</text>\r\n                <text class=\"time-text\">{{item.updateTime}}</text>\r\n              </view>\r\n              <view class=\"card-type-tag\">\r\n                <text>{{item.cardType}}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"footer-right\">\r\n              <view class=\"action-buttons\">\r\n                <view class=\"action-btn\" catchtap=\"onHideCard\" data-index=\"{{index}}\">\r\n                  <text class=\"action-icon\">\uD83D\uDC41\uFE0F</text>\r\n                </view>\r\n                <view class=\"action-btn\" catchtap=\"onEditCard\" data-index=\"{{index}}\">\r\n                  <text class=\"action-icon\">✏\uFE0F</text>\r\n                </view>\r\n                <view class=\"action-btn\" catchtap=\"onDeleteCard\" data-index=\"{{index}}\">\r\n                  <text class=\"action-icon\">\uD83D\uDDD1\uFE0F</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </block>\r\n    \r\n    <!-- 添加名片按钮 -->\r\n    <view class=\"add-card-btn\" bindtap=\"onAddCard\">\r\n      <view class=\"add-btn-content\">\r\n        <text class=\"add-icon\">+</text>\r\n        <text class=\"add-text\">添加名片</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</view>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxml b/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxml
--- a/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxml	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/pages/user-card/components/user-card-list/user-card-list.wxml	(date 1753954117145)
@@ -1,7 +1,5 @@
-<view wx:if="{{!cardList || cardList.length === 0}}" class="no-cards">
-  <text>暂无名片数据</text>
-</view>
-<view wx:else class="card-list-container">
+<view class="card-list-container">
+  <!-- 名片列表 -->
   <view class="card-list-column">
     <block wx:for="{{cardList}}" wx:key="cardId">
       <view class="business-card">
@@ -74,7 +72,7 @@
         </view>
       </view>
     </block>
-    
+
     <!-- 添加名片按钮 -->
     <view class="add-card-btn" bindtap="onAddCard">
       <view class="add-btn-content">
@@ -83,4 +81,11 @@
       </view>
     </view>
   </view>
+
+  <!-- 空状态提示 -->
+  <view wx:if="{{!cardList || cardList.length === 0}}" class="empty-state">
+    <view class="empty-icon">📇</view>
+    <text class="empty-text">暂无名片数据</text>
+    <text class="empty-desc">点击下方按钮创建您的第一张名片</text>
+  </view>
 </view>
\ No newline at end of file
Index: ../weapp/app.json
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>{\r\n  \"pages\": [\r\n    \"pages/index/index\",\r\n    \"pages/map/map\",\r\n    \"pages/publish/category/index\",\r\n    \"pages/publish/form/index\",\r\n    \"pages/publish/publish\",\r\n    \"pages/ai/ai\",\r\n    \"pages/local/local\",\r\n    \"pages/mine/mine\",\r\n    \"pages/mine/profile/index\",\r\n    \"pages/mine/my-posts/index\",\r\n    \"pages/mine/feedback/feedback\",\r\n    \"pages/mine/signin/signin\",\r\n    \"pages/institution-detail/institution-detail\",\r\n    \"pages/mine/signin-records/signin-records\",\r\n    \"pages/post-detail/post-detail\",\r\n    \"pkg_merchant/pages/settle/settle\",\r\n    \"pages/publish/success/success\"\r\n  ],\r\n  \"subpackages\": [\r\n    {\r\n      \"root\": \"pkg_user\",\r\n      \"name\": \"user\",\r\n      \"pages\": [\r\n        \"pages/user-detail/user-detail\",\r\n        \"pages/group/index\",\r\n        \"pages/message/message\",\r\n        \"pages/apply-settle/apply-settle\",\r\n        \"pages/business-card/business-card\",\r\n        \"pages/user-card/user-card\",\r\n        \"pages/add-card/add-card\",\r\n        \"pages/card-detail/card-detail\",\r\n        \"pages/cooperation/cooperation\",\r\n        \"pages/apply_settle/apply_settle\",\r\n        \"pages/cooperation-policy/cooperation-policy\",\r\n        \"pages/add-cardInfo/add-cardInfo\"\r\n      ]\r\n    },\r\n    {\r\n      \"root\": \"pkg_publish\",\r\n      \"name\": \"publish\",\r\n      \"pages\": [\r\n        \"pages/drafts/drafts\",\r\n        \"pages/category/category\",\r\n        \"pages/categories/categories\",\r\n        \"pages/success/success\"\r\n      ]\r\n    },\r\n    {\r\n      \"root\": \"pkg_common\",\r\n      \"name\": \"common\",\r\n      \"pages\": [\r\n        \"pages/post/detail/detail\",\r\n        \"pages/login/login\",\r\n        \"pages/search/search\",\r\n        \"pages/ai/chat/chat\",\r\n        \"pages/login/protocol/protocol\"\r\n      ]\r\n    }\r\n  ],\r\n  \"window\": {\r\n    \"navigationBarBackgroundColor\": \"#FFF\",\r\n    \"navigationBarTextStyle\": \"black\",\r\n    \"navigationBarTitleText\": \"易贴易找\"\r\n  },\r\n  \"style\": \"v2\",\r\n  \"sitemapLocation\": \"sitemap.json\",\r\n  \"permission\": {\r\n    \"scope.userLocation\": {\r\n      \"desc\": \"你的位置信息将用于小程序位置接口的效果展示\"\r\n    }\r\n  },\r\n  \"requiredPrivateInfos\": [\"chooseLocation\", \"getLocation\"]\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/app.json b/../weapp/app.json
--- a/../weapp/app.json	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/app.json	(date 1753954491245)
@@ -23,6 +23,7 @@
       "root": "pkg_user",
       "name": "user",
       "pages": [
+        "pages/user-card/add-cardInfo/add-cardInfo",
         "pages/user-detail/user-detail",
         "pages/group/index",
         "pages/message/message",
@@ -32,7 +33,6 @@
         "pages/add-card/add-card",
         "pages/card-detail/card-detail",
         "pages/cooperation/cooperation",
-        "pages/apply_settle/apply_settle",
         "pages/cooperation-policy/cooperation-policy",
         "pages/add-cardInfo/add-cardInfo"
       ]
Index: ../weapp/docs/用户名片页面接口接入指南.md
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/docs/用户名片页面接口接入指南.md b/../weapp/docs/用户名片页面接口接入指南.md
new file mode 100644
--- /dev/null	(date 1753950660503)
+++ b/../weapp/docs/用户名片页面接口接入指南.md	(date 1753950660503)
@@ -0,0 +1,409 @@
+# 用户名片页面接口接入指南
+
+## 概述
+
+本文档介绍了用户名片页面与后端接口的完整接入方案，包括名片管理和我的帖子查询功能，严格按照小程序开发规范实现。
+
+## 功能特性
+
+### ✨ **核心功能**
+- **我的名片管理**：查看、创建、编辑、删除个人名片
+- **我的帖子查询**：分页查询当前用户发布的所有帖子
+- **数据状态管理**：完善的加载、错误、空状态处理
+- **下拉刷新**：支持下拉刷新数据
+- **上拉加载**：支持分页加载更多帖子
+
+### 🔧 **技术实现**
+- **Store 模式**：使用 businessCardStore.js 统一管理数据请求
+- **组件化设计**：保留原有 user-card-list 组件
+- **响应式布局**：适配不同屏幕尺寸
+- **错误处理**：完善的异常处理和用户提示
+
+## 后端接口
+
+### 📡 **新增接口**
+
+#### 1. 获取当前用户名片
+```http
+GET /blade-chat/card/my-card
+```
+
+**响应示例：**
+```json
+{
+  "success": true,
+  "data": {
+    "id": 1,
+    "company": "上海觅知房地产有限公司",
+    "jobTitle": "总经理",
+    "businessProfile": "专注于房地产开发与销售",
+    "fullName": "觅知君",
+    "gender": 1,
+    "phone": "123 4567 8910",
+    "address": "上海市浦东新区秀浦路11号",
+    "email": "<EMAIL>",
+    "website": "www.51miz.com",
+    "weixin": "mizhi001",
+    "avatar": "/assets/images/def-avatar.png"
+  }
+}
+```
+
+#### 2. 获取我的帖子列表
+```http
+GET /blade-chat/card/my-posts?current=1&size=10
+```
+
+**响应示例：**
+```json
+{
+  "success": true,
+  "data": {
+    "records": [
+      {
+        "id": 1,
+        "title": "房产投资咨询",
+        "content": "提供专业的房产投资建议...",
+        "auditStatus": 1,
+        "createTime": "2024-01-20 14:30",
+        "viewCount": 128,
+        "likeCount": 15
+      }
+    ],
+    "total": 25,
+    "current": 1,
+    "size": 10
+  }
+}
+```
+
+#### 3. 保存或更新名片
+```http
+POST /blade-chat/card/submit
+```
+
+**请求体：**
+```json
+{
+  "id": 1,
+  "company": "公司名称",
+  "jobTitle": "职位",
+  "businessProfile": "业务简介",
+  "fullName": "姓名",
+  "gender": 1,
+  "phone": "电话",
+  "address": "地址",
+  "email": "邮箱",
+  "website": "网址",
+  "weixin": "微信号",
+  "avatar": "头像URL"
+}
+```
+
+#### 4. 删除名片
+```http
+POST /blade-chat/card/remove
+```
+
+**请求体：**
+```json
+{
+  "ids": "1,2,3"
+}
+```
+
+#### 5. 获取名片详情
+```http
+GET /blade-chat/card/detail?id=1
+```
+
+#### 6. 分页查询名片列表
+```http
+GET /blade-chat/card/list?current=1&size=10
+```
+
+## 前端实现
+
+### 📁 **文件结构**
+```
+weapp/pkg_user/pages/user-card/
+├── user-card.js          # 页面逻辑
+├── user-card.wxml        # 页面结构
+├── user-card.wxss        # 页面样式
+├── user-card.json        # 页面配置
+└── components/
+    └── user-card-list/   # 名片列表组件
+```
+
+### 🔄 **Store 管理**
+```javascript
+// weapp/stores/businessCardStore.js
+class BusinessCardStore {
+  // 获取我的名片
+  async getMyCard()
+  
+  // 获取我的帖子
+  async getMyPosts(params)
+  
+  // 保存名片
+  async saveCard(cardData)
+  
+  // 删除名片
+  async deleteCard(ids)
+  
+  // 获取名片详情
+  async getCardDetail(id)
+  
+  // 获取名片列表
+  async getCardList(params)
+}
+```
+
+### 📱 **页面功能**
+
+#### 1. 数据加载
+```javascript
+// 页面加载时自动获取数据
+onLoad() {
+  this.businessCardStore = businessCardStore;
+  this.loadPageData();
+}
+
+// 并行加载名片和帖子数据
+async loadPageData() {
+  const [cardResult, postsResult] = await Promise.all([
+    this.loadMyCard(),
+    this.loadMyPosts(true)
+  ]);
+}
+```
+
+#### 2. 名片管理
+```javascript
+// 保存名片
+async saveCard(cardData) {
+  const result = await this.businessCardStore.saveCard(cardData);
+  if (result.success) {
+    wx.showToast({ title: '保存成功', icon: 'success' });
+    await this.loadMyCard();
+  }
+}
+
+// 删除名片
+async deleteCard(cardId) {
+  const result = await wx.showModal({
+    title: '确认删除',
+    content: '确定要删除这张名片吗？'
+  });
+  
+  if (result.confirm) {
+    const deleteResult = await this.businessCardStore.deleteCard(cardId);
+    if (deleteResult.success) {
+      wx.showToast({ title: '删除成功', icon: 'success' });
+      await this.loadPageData();
+    }
+  }
+}
+```
+
+#### 3. 帖子管理
+```javascript
+// 分页加载帖子
+async loadMyPosts(reset = false) {
+  if (reset) {
+    this.setData({
+      currentPage: 1,
+      hasMore: true,
+      myPosts: []
+    });
+  }
+
+  const result = await this.businessCardStore.getMyPosts({
+    current: this.data.currentPage,
+    size: this.data.pageSize
+  });
+  
+  if (result.success) {
+    const newPosts = result.data.records || [];
+    const allPosts = reset ? newPosts : [...this.data.myPosts, ...newPosts];
+    
+    this.setData({
+      myPosts: allPosts,
+      currentPage: this.data.currentPage + 1,
+      hasMore: newPosts.length >= this.data.pageSize
+    });
+  }
+}
+```
+
+#### 4. 交互功能
+```javascript
+// 下拉刷新
+async onPullDownRefresh() {
+  await this.loadPageData();
+  wx.stopPullDownRefresh();
+}
+
+// 上拉加载更多
+async onReachBottom() {
+  if (!this.data.loading && this.data.hasMore) {
+    await this.loadMyPosts(false);
+  }
+}
+```
+
+## 页面结构
+
+### 🎨 **WXML 结构**
+```xml
+<view class="user-card-container">
+  <!-- 加载状态 -->
+  <view wx:if="{{loading}}" class="loading-container">
+    <view class="loading-spinner"></view>
+    <text class="loading-text">加载中...</text>
+  </view>
+
+  <!-- 主要内容 -->
+  <view wx:else class="main-content">
+    <!-- 我的名片区域 -->
+    <view class="my-card-section">
+      <view class="section-header">
+        <text class="section-title">我的名片</text>
+        <button class="edit-btn" bindtap="goToEditCard">
+          {{myCard && myCard.id ? '编辑' : '创建'}}
+        </button>
+      </view>
+      
+      <!-- 名片内容或空状态 -->
+      <view wx:if="{{myCard && myCard.id}}" class="card-content">
+        <!-- 名片信息展示 -->
+      </view>
+      <view wx:else class="empty-card">
+        <text class="empty-text">还没有创建名片</text>
+        <button class="create-btn" bindtap="goToEditCard">立即创建</button>
+      </view>
+    </view>
+
+    <!-- 我的帖子区域 -->
+    <view class="my-posts-section">
+      <view class="section-header">
+        <text class="section-title">我的帖子 ({{myPosts.length}})</text>
+        <button class="publish-btn" bindtap="goToPublishPost">发布</button>
+      </view>
+      
+      <!-- 帖子列表或空状态 -->
+      <view wx:if="{{myPosts.length > 0}}" class="posts-list">
+        <view wx:for="{{myPosts}}" wx:key="id" class="post-item">
+          <!-- 帖子信息展示 -->
+        </view>
+      </view>
+      <view wx:else class="empty-posts">
+        <text class="empty-text">还没有发布帖子</text>
+        <button class="publish-btn" bindtap="goToPublishPost">发布第一个帖子</button>
+      </view>
+    </view>
+  </view>
+</view>
+```
+
+## 样式设计
+
+### 🎨 **关键样式**
+```css
+/* 加载动画 */
+.loading-spinner {
+  width: 60rpx;
+  height: 60rpx;
+  border: 4rpx solid #e0e0e0;
+  border-top: 4rpx solid #1976d2;
+  border-radius: 50%;
+  animation: spin 1s linear infinite;
+}
+
+/* 名片区域 */
+.my-card-section {
+  background: white;
+  border-radius: 16rpx;
+  padding: 30rpx;
+  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
+}
+
+/* 帖子状态 */
+.post-status.approved {
+  background-color: #e8f5e8;
+  color: #4caf50;
+}
+
+.post-status.rejected {
+  background-color: #ffebee;
+  color: #f44336;
+}
+
+.post-status.pending {
+  background-color: #fff3e0;
+  color: #ff9800;
+}
+```
+
+## 开发规范遵循
+
+### ✅ **规范要点**
+
+1. **Store 模式**
+   - 所有接口调用统一在 `businessCardStore.js` 中管理
+   - 页面 JS 文件专注于视图交互和生命周期
+   - 实现了数据逻辑与视图逻辑的分离
+
+2. **组件化设计**
+   - 保留了原有的 `user-card-list` 组件
+   - 组件放在页面私有的 `components` 目录下
+   - 使用相对路径引用组件
+
+3. **错误处理**
+   - 完善的 try-catch 异常处理
+   - 用户友好的错误提示
+   - 网络请求失败的重试机制
+
+4. **性能优化**
+   - 分页加载避免一次性加载大量数据
+   - 并行请求提高加载效率
+   - 合理的加载状态管理
+
+## 测试验证
+
+### 🧪 **功能测试**
+
+1. **名片管理测试**
+   - [ ] 首次进入页面显示"创建名片"
+   - [ ] 创建名片后显示名片信息
+   - [ ] 编辑名片功能正常
+   - [ ] 删除名片需要确认
+
+2. **帖子查询测试**
+   - [ ] 正确显示帖子数量
+   - [ ] 帖子状态显示正确（待审核/已通过/未通过）
+   - [ ] 分页加载功能正常
+   - [ ] 空状态显示正确
+
+3. **交互测试**
+   - [ ] 下拉刷新功能正常
+   - [ ] 上拉加载更多功能正常
+   - [ ] 跳转到编辑页面
+   - [ ] 跳转到发布帖子页面
+
+4. **异常处理测试**
+   - [ ] 网络异常时显示错误信息
+   - [ ] 重试按钮功能正常
+   - [ ] 加载状态显示正确
+
+## 总结
+
+通过本次接入，用户名片页面实现了：
+
+- ✅ **完整的名片管理功能**：增删改查一应俱全
+- ✅ **我的帖子查询功能**：支持分页和状态显示
+- ✅ **规范的代码结构**：严格遵循开发规范
+- ✅ **良好的用户体验**：加载状态、错误处理、交互反馈
+- ✅ **可维护的架构**：Store 模式、组件化设计
+
+这个实现为后续的功能扩展奠定了良好的基础，同时保持了代码的可读性和可维护性。
Index: ../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.wxml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><!--添加名片页面-->\r\n<view class=\"add-card-container\">\r\n  <view class=\"form-container\">\r\n    <scroll-view class=\"form-scroll\" scroll-y=\"true\">\r\n      <view class=\"form-section\">\r\n        <view class=\"section-title\">基本信息</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">名片ID *</text>\r\n          <input class=\"form-input\" placeholder=\"请输入名片ID\" value=\"{{formData.cardId}}\" bindinput=\"onInputChange\" data-field=\"cardId\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">公司名称 *</text>\r\n          <input class=\"form-input\" placeholder=\"请输入公司名称\" value=\"{{formData.company}}\" bindinput=\"onInputChange\" data-field=\"company\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">职位</text>\r\n          <input class=\"form-input\" placeholder=\"请输入职位\" value=\"{{formData.position}}\" bindinput=\"onInputChange\" data-field=\"position\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">姓名 *</text>\r\n          <input class=\"form-input\" placeholder=\"请输入姓名\" value=\"{{formData.name}}\" bindinput=\"onInputChange\" data-field=\"name\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">性别</text>\r\n          <picker class=\"form-picker\" range=\"{{genderOptions}}\" value=\"{{formData.gender}}\" bindchange=\"onPickerChange\" data-field=\"gender\">\r\n            <view class=\"picker-text\">{{genderOptions[formData.gender]}}</view>\r\n          </picker>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"form-section\">\r\n        <view class=\"section-title\">联系信息</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">电话</text>\r\n          <input class=\"form-input\" placeholder=\"请输入电话\" value=\"{{formData.phone}}\" bindinput=\"onInputChange\" data-field=\"phone\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">地址</text>\r\n          <input class=\"form-input\" placeholder=\"请输入地址\" value=\"{{formData.address}}\" bindinput=\"onInputChange\" data-field=\"address\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">邮箱</text>\r\n          <input class=\"form-input\" placeholder=\"请输入邮箱\" value=\"{{formData.email}}\" bindinput=\"onInputChange\" data-field=\"email\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">网址</text>\r\n          <input class=\"form-input\" placeholder=\"请输入网址\" value=\"{{formData.website}}\" bindinput=\"onInputChange\" data-field=\"website\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">微信</text>\r\n          <input class=\"form-input\" placeholder=\"请输入微信\" value=\"{{formData.wechat}}\" bindinput=\"onInputChange\" data-field=\"wechat\"/>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"form-section\">\r\n        <view class=\"section-title\">媒体资源</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">头像URL</text>\r\n          <input class=\"form-input\" placeholder=\"请输入头像URL\" value=\"{{formData.avatar}}\" bindinput=\"onInputChange\" data-field=\"avatar\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">图片URL</text>\r\n          <input class=\"form-input\" placeholder=\"请输入图片URL\" value=\"{{formData.images}}\" bindinput=\"onInputChange\" data-field=\"images\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">视频URL</text>\r\n          <input class=\"form-input\" placeholder=\"请输入视频URL\" value=\"{{formData.videos}}\" bindinput=\"onInputChange\" data-field=\"videos\"/>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"form-section\">\r\n        <view class=\"section-title\">业务信息</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">业务简介</text>\r\n          <textarea class=\"form-textarea\" placeholder=\"请输入业务简介\" value=\"{{formData.businessIntro}}\" bindinput=\"onInputChange\" data-field=\"businessIntro\"/>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">备注信息</text>\r\n          <textarea class=\"form-textarea\" placeholder=\"请输入备注信息\" value=\"{{formData.remark}}\" bindinput=\"onInputChange\" data-field=\"remark\"/>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"form-section\">\r\n        <view class=\"section-title\">设置</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">是否公开</text>\r\n          <picker class=\"form-picker\" range=\"{{publicOptions}}\" value=\"{{formData.isPublic}}\" bindchange=\"onPickerChange\" data-field=\"isPublic\">\r\n            <view class=\"picker-text\">{{publicOptions[formData.isPublic]}}</view>\r\n          </picker>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">审核状态</text>\r\n          <picker class=\"form-picker\" range=\"{{auditOptions}}\" value=\"{{formData.auditStatus}}\" bindchange=\"onPickerChange\" data-field=\"auditStatus\">\r\n            <view class=\"picker-text\">{{auditOptions[formData.auditStatus]}}</view>\r\n          </picker>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">名片类型</text>\r\n          <input class=\"form-input\" placeholder=\"请输入名片类型\" value=\"{{formData.cardType}}\" bindinput=\"onInputChange\" data-field=\"cardType\"/>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n\r\n  <view class=\"footer-actions\">\r\n    <button class=\"btn-cancel\" bindtap=\"onCancel\">取消</button>\r\n    <button class=\"btn-confirm\" bindtap=\"onConfirm\">确认添加</button>\r\n  </view>\r\n</view>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.wxml b/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.wxml
--- a/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.wxml	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.wxml	(date 1753954500261)
@@ -1,127 +1,2 @@
-<!--添加名片页面-->
-<view class="add-card-container">
-  <view class="form-container">
-    <scroll-view class="form-scroll" scroll-y="true">
-      <view class="form-section">
-        <view class="section-title">基本信息</view>
-        
-        <view class="form-item">
-          <text class="form-label">名片ID *</text>
-          <input class="form-input" placeholder="请输入名片ID" value="{{formData.cardId}}" bindinput="onInputChange" data-field="cardId"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">公司名称 *</text>
-          <input class="form-input" placeholder="请输入公司名称" value="{{formData.company}}" bindinput="onInputChange" data-field="company"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">职位</text>
-          <input class="form-input" placeholder="请输入职位" value="{{formData.position}}" bindinput="onInputChange" data-field="position"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">姓名 *</text>
-          <input class="form-input" placeholder="请输入姓名" value="{{formData.name}}" bindinput="onInputChange" data-field="name"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">性别</text>
-          <picker class="form-picker" range="{{genderOptions}}" value="{{formData.gender}}" bindchange="onPickerChange" data-field="gender">
-            <view class="picker-text">{{genderOptions[formData.gender]}}</view>
-          </picker>
-        </view>
-      </view>
-
-      <view class="form-section">
-        <view class="section-title">联系信息</view>
-        
-        <view class="form-item">
-          <text class="form-label">电话</text>
-          <input class="form-input" placeholder="请输入电话" value="{{formData.phone}}" bindinput="onInputChange" data-field="phone"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">地址</text>
-          <input class="form-input" placeholder="请输入地址" value="{{formData.address}}" bindinput="onInputChange" data-field="address"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">邮箱</text>
-          <input class="form-input" placeholder="请输入邮箱" value="{{formData.email}}" bindinput="onInputChange" data-field="email"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">网址</text>
-          <input class="form-input" placeholder="请输入网址" value="{{formData.website}}" bindinput="onInputChange" data-field="website"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">微信</text>
-          <input class="form-input" placeholder="请输入微信" value="{{formData.wechat}}" bindinput="onInputChange" data-field="wechat"/>
-        </view>
-      </view>
-
-      <view class="form-section">
-        <view class="section-title">媒体资源</view>
-        
-        <view class="form-item">
-          <text class="form-label">头像URL</text>
-          <input class="form-input" placeholder="请输入头像URL" value="{{formData.avatar}}" bindinput="onInputChange" data-field="avatar"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">图片URL</text>
-          <input class="form-input" placeholder="请输入图片URL" value="{{formData.images}}" bindinput="onInputChange" data-field="images"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">视频URL</text>
-          <input class="form-input" placeholder="请输入视频URL" value="{{formData.videos}}" bindinput="onInputChange" data-field="videos"/>
-        </view>
-      </view>
-
-      <view class="form-section">
-        <view class="section-title">业务信息</view>
-        
-        <view class="form-item">
-          <text class="form-label">业务简介</text>
-          <textarea class="form-textarea" placeholder="请输入业务简介" value="{{formData.businessIntro}}" bindinput="onInputChange" data-field="businessIntro"/>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">备注信息</text>
-          <textarea class="form-textarea" placeholder="请输入备注信息" value="{{formData.remark}}" bindinput="onInputChange" data-field="remark"/>
-        </view>
-      </view>
-
-      <view class="form-section">
-        <view class="section-title">设置</view>
-        
-        <view class="form-item">
-          <text class="form-label">是否公开</text>
-          <picker class="form-picker" range="{{publicOptions}}" value="{{formData.isPublic}}" bindchange="onPickerChange" data-field="isPublic">
-            <view class="picker-text">{{publicOptions[formData.isPublic]}}</view>
-          </picker>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">审核状态</text>
-          <picker class="form-picker" range="{{auditOptions}}" value="{{formData.auditStatus}}" bindchange="onPickerChange" data-field="auditStatus">
-            <view class="picker-text">{{auditOptions[formData.auditStatus]}}</view>
-          </picker>
-        </view>
-        
-        <view class="form-item">
-          <text class="form-label">名片类型</text>
-          <input class="form-input" placeholder="请输入名片类型" value="{{formData.cardType}}" bindinput="onInputChange" data-field="cardType"/>
-        </view>
-      </view>
-    </scroll-view>
-  </view>
-
-  <view class="footer-actions">
-    <button class="btn-cancel" bindtap="onCancel">取消</button>
-    <button class="btn-confirm" bindtap="onConfirm">确认添加</button>
-  </view>
-</view>
\ No newline at end of file
+<!--pkg_user/pages/add-cardInfo/add-cardInfo.wxml-->
+<text>pkg_user/pages/add-cardInfo/add-cardInfo.wxml</text>
\ No newline at end of file
Index: ../weapp/pkg_user/pages/card-detail/card-detail.wxml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><!-- pkg_user/pages/card-detail/card-detail.wxml -->\r\n<view class=\"card-detail-container\">\r\n  <!-- 加载状态 -->\r\n  <view wx:if=\"{{loading}}\" class=\"loading-container\">\r\n    <view class=\"loading-spinner\"></view>\r\n    <text class=\"loading-text\">正在加载名片...</text>\r\n  </view>\r\n\r\n  <!-- 名片内容 -->\r\n  <view wx:else class=\"card-content\">\r\n    <!-- 头部信息区域 -->\r\n    <view class=\"header-section\">\r\n      <!-- 头像 -->\r\n      <view class=\"avatar-container\">\r\n        <image\r\n          src=\"{{cardInfo.avatar || '/assets/images/bot-avatar.png'}}\"\r\n          class=\"avatar-image\"\r\n          mode=\"aspectFill\"\r\n        />\r\n      </view>\r\n\r\n      <!-- 基本信息 -->\r\n      <view class=\"basic-info\">\r\n        <view class=\"name-row\">\r\n          <text class=\"name\">{{cardInfo.name}}</text>\r\n          <text wx:if=\"{{cardInfo.gender === 1}}\" class=\"gender male\">♂</text>\r\n          <text wx:elif=\"{{cardInfo.gender === 2}}\" class=\"gender female\">♀</text>\r\n        </view>\r\n        <text class=\"position\">{{cardInfo.position}}</text>\r\n        <text class=\"company\">{{cardInfo.company}}</text>\r\n        <text class=\"user-id\">ID: {{cardInfo.userId}}</text>\r\n      </view>\r\n\r\n      <!-- 状态标签 -->\r\n      <view class=\"status-tags\">\r\n        <text wx:if=\"{{cardInfo.isPublic}}\" class=\"tag public\">已认证</text>\r\n        <text class=\"tag open\">对外公开</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 业务简介 -->\r\n    <view class=\"intro-section\" wx:if=\"{{cardInfo.businessIntro}}\">\r\n      <text class=\"intro-text\">{{cardInfo.businessIntro}}</text>\r\n      <text class=\"update-time\">更新于 {{cardInfo.remark}}</text>\r\n    </view>\r\n\r\n    <!-- 联系方式 -->\r\n    <view class=\"contact-section\">\r\n      <view class=\"contact-row\">\r\n        <view class=\"contact-item\" wx:if=\"{{cardInfo.phone}}\" bindtap=\"onCallPhone\">\r\n          <text class=\"contact-icon\">\uD83D\uDCF1</text>\r\n          <view class=\"contact-info\">\r\n            <text class=\"contact-label\">电话</text>\r\n            <text class=\"contact-value\">{{cardInfo.phone}}</text>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"contact-item\" wx:if=\"{{cardInfo.wechat}}\" bindtap=\"onCopyWechat\">\r\n          <text class=\"contact-icon\">\uD83D\uDCAC</text>\r\n          <view class=\"contact-info\">\r\n            <text class=\"contact-label\">微信</text>\r\n            <text class=\"contact-value\">{{cardInfo.wechat}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"contact-row\">\r\n        <view class=\"contact-item\" wx:if=\"{{cardInfo.email}}\" bindtap=\"onSendEmail\">\r\n          <text class=\"contact-icon\">\uD83D\uDCE7</text>\r\n          <view class=\"contact-info\">\r\n            <text class=\"contact-label\">邮箱</text>\r\n            <text class=\"contact-value\">{{cardInfo.email}}</text>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"contact-item\" wx:if=\"{{cardInfo.website}}\" bindtap=\"onViewWebsite\">\r\n          <text class=\"contact-icon\">\uD83C\uDF10</text>\r\n          <view class=\"contact-info\">\r\n            <text class=\"contact-label\">网站</text>\r\n            <text class=\"contact-value\">{{cardInfo.website}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"contact-item full-width\" wx:if=\"{{cardInfo.address}}\" bindtap=\"onViewAddress\">\r\n        <text class=\"contact-icon\">\uD83D\uDCCD</text>\r\n        <view class=\"contact-info\">\r\n          <text class=\"contact-label\">地址</text>\r\n          <text class=\"contact-value\">{{cardInfo.address}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 相关媒体 -->\r\n    <view class=\"media-section\" wx:if=\"{{cardInfo.images && cardInfo.images.length > 0}}\">\r\n      <text class=\"section-title\">相关媒体</text>\r\n      <view class=\"media-grid\">\r\n        <view class=\"media-item\" wx:for=\"{{cardInfo.images}}\" wx:key=\"index\">\r\n          <image src=\"{{item}}\" class=\"media-image\" mode=\"aspectFill\" bindtap=\"onPreviewImage\" data-src=\"{{item}}\" />\r\n          <view class=\"media-overlay\">\r\n            <text class=\"play-icon\">▶\uFE0F</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 名片类型 -->\r\n    <view class=\"footer-section\">\r\n      <view class=\"footer-row\">\r\n        <text class=\"footer-label\">名片类型</text>\r\n        <text class=\"footer-value owner\">房产经纪人</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</view>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/pages/card-detail/card-detail.wxml b/../weapp/pkg_user/pages/card-detail/card-detail.wxml
--- a/../weapp/pkg_user/pages/card-detail/card-detail.wxml	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/pages/card-detail/card-detail.wxml	(date 1753954500258)
@@ -1,115 +1,2 @@
-<!-- pkg_user/pages/card-detail/card-detail.wxml -->
-<view class="card-detail-container">
-  <!-- 加载状态 -->
-  <view wx:if="{{loading}}" class="loading-container">
-    <view class="loading-spinner"></view>
-    <text class="loading-text">正在加载名片...</text>
-  </view>
-
-  <!-- 名片内容 -->
-  <view wx:else class="card-content">
-    <!-- 头部信息区域 -->
-    <view class="header-section">
-      <!-- 头像 -->
-      <view class="avatar-container">
-        <image
-          src="{{cardInfo.avatar || '/assets/images/bot-avatar.png'}}"
-          class="avatar-image"
-          mode="aspectFill"
-        />
-      </view>
-
-      <!-- 基本信息 -->
-      <view class="basic-info">
-        <view class="name-row">
-          <text class="name">{{cardInfo.name}}</text>
-          <text wx:if="{{cardInfo.gender === 1}}" class="gender male">♂</text>
-          <text wx:elif="{{cardInfo.gender === 2}}" class="gender female">♀</text>
-        </view>
-        <text class="position">{{cardInfo.position}}</text>
-        <text class="company">{{cardInfo.company}}</text>
-        <text class="user-id">ID: {{cardInfo.userId}}</text>
-      </view>
-
-      <!-- 状态标签 -->
-      <view class="status-tags">
-        <text wx:if="{{cardInfo.isPublic}}" class="tag public">已认证</text>
-        <text class="tag open">对外公开</text>
-      </view>
-    </view>
-
-    <!-- 业务简介 -->
-    <view class="intro-section" wx:if="{{cardInfo.businessIntro}}">
-      <text class="intro-text">{{cardInfo.businessIntro}}</text>
-      <text class="update-time">更新于 {{cardInfo.remark}}</text>
-    </view>
-
-    <!-- 联系方式 -->
-    <view class="contact-section">
-      <view class="contact-row">
-        <view class="contact-item" wx:if="{{cardInfo.phone}}" bindtap="onCallPhone">
-          <text class="contact-icon">📱</text>
-          <view class="contact-info">
-            <text class="contact-label">电话</text>
-            <text class="contact-value">{{cardInfo.phone}}</text>
-          </view>
-        </view>
-
-        <view class="contact-item" wx:if="{{cardInfo.wechat}}" bindtap="onCopyWechat">
-          <text class="contact-icon">💬</text>
-          <view class="contact-info">
-            <text class="contact-label">微信</text>
-            <text class="contact-value">{{cardInfo.wechat}}</text>
-          </view>
-        </view>
-      </view>
-
-      <view class="contact-row">
-        <view class="contact-item" wx:if="{{cardInfo.email}}" bindtap="onSendEmail">
-          <text class="contact-icon">📧</text>
-          <view class="contact-info">
-            <text class="contact-label">邮箱</text>
-            <text class="contact-value">{{cardInfo.email}}</text>
-          </view>
-        </view>
-
-        <view class="contact-item" wx:if="{{cardInfo.website}}" bindtap="onViewWebsite">
-          <text class="contact-icon">🌐</text>
-          <view class="contact-info">
-            <text class="contact-label">网站</text>
-            <text class="contact-value">{{cardInfo.website}}</text>
-          </view>
-        </view>
-      </view>
-
-      <view class="contact-item full-width" wx:if="{{cardInfo.address}}" bindtap="onViewAddress">
-        <text class="contact-icon">📍</text>
-        <view class="contact-info">
-          <text class="contact-label">地址</text>
-          <text class="contact-value">{{cardInfo.address}}</text>
-        </view>
-      </view>
-    </view>
-
-    <!-- 相关媒体 -->
-    <view class="media-section" wx:if="{{cardInfo.images && cardInfo.images.length > 0}}">
-      <text class="section-title">相关媒体</text>
-      <view class="media-grid">
-        <view class="media-item" wx:for="{{cardInfo.images}}" wx:key="index">
-          <image src="{{item}}" class="media-image" mode="aspectFill" bindtap="onPreviewImage" data-src="{{item}}" />
-          <view class="media-overlay">
-            <text class="play-icon">▶️</text>
-          </view>
-        </view>
-      </view>
-    </view>
-
-    <!-- 名片类型 -->
-    <view class="footer-section">
-      <view class="footer-row">
-        <text class="footer-label">名片类型</text>
-        <text class="footer-value owner">房产经纪人</text>
-      </view>
-    </view>
-  </view>
-</view>
+<!--pkg_user/pages/card-detail/card-detail.wxml-->
+<text>pkg_user/pages/card-detail/card-detail.wxml</text>
\ No newline at end of file
Index: ../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>Page({\r\n  data: {\r\n    formData: {\r\n      cardId: '',\r\n      company: '',\r\n      position: '',\r\n      businessIntro: '',\r\n      name: '',\r\n      gender: 1,\r\n      phone: '',\r\n      address: '',\r\n      email: '',\r\n      website: '',\r\n      wechat: '',\r\n      avatar: '/assets/images/def-avatar.png',\r\n      logo: '/assets/images/bot-avatar.png',\r\n      qrCode: '/assets/images/bot-avatar.png',\r\n      images: '',\r\n      videos: '',\r\n      remark: '',\r\n      isPublic: 1,\r\n      updateTime: '',\r\n      auditStatus: 1,\r\n      cardType: ''\r\n    },\r\n    genderOptions: ['保密', '男', '女'],\r\n    publicOptions: ['否', '是'],\r\n    auditOptions: ['待审核', '已通过', '未通过']\r\n  },\r\n\r\n  onLoad() {\r\n    // 设置默认的更新时间\r\n    this.setDefaultUpdateTime();\r\n  },\r\n\r\n  // 设置默认更新时间\r\n  setDefaultUpdateTime() {\r\n    const now = new Date();\r\n    const updateTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;\r\n    \r\n    this.setData({\r\n      'formData.updateTime': updateTime\r\n    });\r\n  },\r\n\r\n  // 输入框变化处理\r\n  onInputChange(e) {\r\n    const field = e.currentTarget.dataset.field;\r\n    const value = e.detail.value;\r\n    this.setData({\r\n      [`formData.${field}`]: value\r\n    });\r\n  },\r\n\r\n  // 选择器变化处理\r\n  onPickerChange(e) {\r\n    const field = e.currentTarget.dataset.field;\r\n    const value = parseInt(e.detail.value);\r\n    this.setData({\r\n      [`formData.${field}`]: value\r\n    });\r\n  },\r\n\r\n  // 取消操作\r\n  onCancel() {\r\n    wx.showModal({\r\n      title: '确认取消',\r\n      content: '确定要取消添加名片吗？已填写的内容将丢失。',\r\n      confirmColor: '#ff8080',\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          wx.navigateBack();\r\n        }\r\n      }\r\n    });\r\n  },\r\n\r\n  // 确认添加\r\n  onConfirm() {\r\n    const formData = this.data.formData;\r\n    \r\n    // 验证必填字段\r\n    if (!formData.cardId.trim()) {\r\n      wx.showToast({\r\n        title: '请输入名片ID',\r\n        icon: 'none'\r\n      });\r\n      return;\r\n    }\r\n    \r\n    if (!formData.company.trim()) {\r\n      wx.showToast({\r\n        title: '请输入公司名称',\r\n        icon: 'none'\r\n      });\r\n      return;\r\n    }\r\n    \r\n    if (!formData.name.trim()) {\r\n      wx.showToast({\r\n        title: '请输入姓名',\r\n        icon: 'none'\r\n      });\r\n      return;\r\n    }\r\n    \r\n    // 处理数组字段\r\n    const newCard = {\r\n      ...formData,\r\n      images: formData.images ? [formData.images] : [],\r\n      videos: formData.videos ? [formData.videos] : []\r\n    };\r\n    \r\n    // 获取页面栈\r\n    const pages = getCurrentPages();\r\n    const prevPage = pages[pages.length - 2]; // 获取上一个页面\r\n    \r\n    if (prevPage && prevPage.onAddCard) {\r\n      // 调用上一个页面的添加方法\r\n      prevPage.onAddCard({ detail: newCard });\r\n    }\r\n    \r\n    wx.showToast({\r\n      title: '添加成功',\r\n      icon: 'success',\r\n      duration: 2000,\r\n      success: () => {\r\n        // 延迟返回，让用户看到成功提示\r\n        setTimeout(() => {\r\n          wx.navigateBack();\r\n        }, 2000);\r\n      }\r\n    });\r\n  },\r\n\r\n  // 页面显示时设置更新时间\r\n  onShow() {\r\n    this.setDefaultUpdateTime();\r\n  }\r\n}); 
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.js b/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.js
--- a/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/pages/add-cardInfo/add-cardInfo.js	(date 1753954500261)
@@ -1,140 +1,66 @@
+// pkg_user/pages/add-cardInfo/add-cardInfo.js
 Page({
+
+  /**
+   * 页面的初始数据
+   */
   data: {
-    formData: {
-      cardId: '',
-      company: '',
-      position: '',
-      businessIntro: '',
-      name: '',
-      gender: 1,
-      phone: '',
-      address: '',
-      email: '',
-      website: '',
-      wechat: '',
-      avatar: '/assets/images/def-avatar.png',
-      logo: '/assets/images/bot-avatar.png',
-      qrCode: '/assets/images/bot-avatar.png',
-      images: '',
-      videos: '',
-      remark: '',
-      isPublic: 1,
-      updateTime: '',
-      auditStatus: 1,
-      cardType: ''
-    },
-    genderOptions: ['保密', '男', '女'],
-    publicOptions: ['否', '是'],
-    auditOptions: ['待审核', '已通过', '未通过']
-  },
-
-  onLoad() {
-    // 设置默认的更新时间
-    this.setDefaultUpdateTime();
-  },
-
-  // 设置默认更新时间
-  setDefaultUpdateTime() {
-    const now = new Date();
-    const updateTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
-    
-    this.setData({
-      'formData.updateTime': updateTime
-    });
-  },
 
-  // 输入框变化处理
-  onInputChange(e) {
-    const field = e.currentTarget.dataset.field;
-    const value = e.detail.value;
-    this.setData({
-      [`formData.${field}`]: value
-    });
   },
 
-  // 选择器变化处理
-  onPickerChange(e) {
-    const field = e.currentTarget.dataset.field;
-    const value = parseInt(e.detail.value);
-    this.setData({
-      [`formData.${field}`]: value
-    });
-  },
+  /**
+   * 生命周期函数--监听页面加载
+   */
+  onLoad(options) {
 
-  // 取消操作
-  onCancel() {
-    wx.showModal({
-      title: '确认取消',
-      content: '确定要取消添加名片吗？已填写的内容将丢失。',
-      confirmColor: '#ff8080',
-      success: (res) => {
-        if (res.confirm) {
-          wx.navigateBack();
-        }
-      }
-    });
   },
 
-  // 确认添加
-  onConfirm() {
-    const formData = this.data.formData;
-    
-    // 验证必填字段
-    if (!formData.cardId.trim()) {
-      wx.showToast({
-        title: '请输入名片ID',
-        icon: 'none'
-      });
-      return;
-    }
-    
-    if (!formData.company.trim()) {
-      wx.showToast({
-        title: '请输入公司名称',
-        icon: 'none'
-      });
-      return;
-    }
-    
-    if (!formData.name.trim()) {
-      wx.showToast({
-        title: '请输入姓名',
-        icon: 'none'
-      });
-      return;
-    }
-    
-    // 处理数组字段
-    const newCard = {
-      ...formData,
-      images: formData.images ? [formData.images] : [],
-      videos: formData.videos ? [formData.videos] : []
-    };
-    
-    // 获取页面栈
-    const pages = getCurrentPages();
-    const prevPage = pages[pages.length - 2]; // 获取上一个页面
-    
-    if (prevPage && prevPage.onAddCard) {
-      // 调用上一个页面的添加方法
-      prevPage.onAddCard({ detail: newCard });
-    }
-    
-    wx.showToast({
-      title: '添加成功',
-      icon: 'success',
-      duration: 2000,
-      success: () => {
-        // 延迟返回，让用户看到成功提示
-        setTimeout(() => {
-          wx.navigateBack();
-        }, 2000);
-      }
-    });
+  /**
+   * 生命周期函数--监听页面初次渲染完成
+   */
+  onReady() {
+
   },
 
-  // 页面显示时设置更新时间
+  /**
+   * 生命周期函数--监听页面显示
+   */
   onShow() {
-    this.setDefaultUpdateTime();
+
+  },
+
+  /**
+   * 生命周期函数--监听页面隐藏
+   */
+  onHide() {
+
+  },
+
+  /**
+   * 生命周期函数--监听页面卸载
+   */
+  onUnload() {
+
+  },
+
+  /**
+   * 页面相关事件处理函数--监听用户下拉动作
+   */
+  onPullDownRefresh() {
+
+  },
+
+  /**
+   * 页面上拉触底事件的处理函数
+   */
+  onReachBottom() {
+
+  },
+
+  /**
+   * 用户点击右上角分享
+   */
+  onShareAppMessage() {
+
   }
-}); 
\ No newline at end of file
+})
\ No newline at end of file
Index: ../weapp/pkg_common/utils/request.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址\r\nconst BASE_URL = 'http://**************'; // 替换为您的后端API地址\r\n\r\nconst { handleLoginExpired } = require('../../utils/loginHandler');\r\n\r\n// 请求拦截器\r\nconst requestInterceptor = (config) => {\r\n  const token = wx.getStorageSync('token').value;\r\n  const openId = wx.getStorageSync('openId');\r\n\r\n  config.header = {\r\n    ...config.header,\r\n    'Tenant-Id' :\"000000\",\r\n    'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,\r\n  };\r\n\r\n  if (token) {\r\n    config.header = {\r\n      ...config.header,\r\n      'Blade-Auth': `Bearer ${token}`\r\n    };\r\n  }\r\n\r\n  // 添加OpenID头\r\n  if (openId) {\r\n    config.header = {\r\n      ...config.header,\r\n      'X-Open-ID': openId\r\n    };\r\n  }\r\n  \r\n  return config;\r\n};\r\n\r\n// 响应拦截器\r\nconst responseInterceptor = (response) => {\r\n  const { data } = response;\r\n  // 处理token过期\r\n  if (data.code === 401) {\r\n    handleLoginExpired();\r\n    return Promise.reject(new Error('登录已过期，请重新登录'));\r\n  }\r\n  \r\n  return data;\r\n};\r\n\r\n// 刷新token\r\nconst refreshToken = () => {\r\n  return new Promise((resolve, reject) => {\r\n    const refreshToken = wx.getStorageSync('refreshToken');\r\n    if (!refreshToken) {\r\n      reject(new Error('未找到刷新token'));\r\n      return;\r\n    }\r\n\r\n    wx.request({\r\n      url: `${BASE_URL}/blade-auth/token`,\r\n      method: 'POST',\r\n      data: {\r\n        grantType: 'refresh_token',\r\n        refreshToken: refreshToken\r\n      },\r\n      success: (res) => {\r\n        if (res.data.success) {\r\n          const { accessToken, refreshToken } = res.data.data;\r\n          wx.setStorageSync('token', accessToken);\r\n          wx.setStorageSync('refreshToken', refreshToken);\r\n          resolve(accessToken);\r\n        } else {\r\n          reject(new Error(res.data.msg));\r\n        }\r\n      },\r\n      fail: reject\r\n    });\r\n  });\r\n};\r\n\r\n// 统一请求方法\r\nconst request = (options) => {\r\n  const config = requestInterceptor(options);\r\n  return new Promise((resolve, reject) => {\r\n    wx.request({\r\n      ...config,\r\n      url: `${BASE_URL}${config.url}`,\r\n        success: (res) => {\r\n        resolve(responseInterceptor(res));\r\n      },\r\n      fail: (err) => {\r\n        reject(err);\r\n      }\r\n    });\r\n  });\r\n};\r\n\r\nmodule.exports = {\r\n  request,\r\n  refreshToken\r\n}; 
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_common/utils/request.js b/../weapp/pkg_common/utils/request.js
--- a/../weapp/pkg_common/utils/request.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_common/utils/request.js	(date 1753950769052)
@@ -1,5 +1,5 @@
 // const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址
-const BASE_URL = 'http://**************'; // 替换为您的后端API地址
+const BASE_URL = 'http://localhost'; // 替换为您的后端API地址
 
 const { handleLoginExpired } = require('../../utils/loginHandler');
 
Index: ../weapp/pkg_user/pages/card-detail/card-detail.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// pkg_user/pages/card-detail/card-detail.js\r\nconst { request } = require('../../../utils/request');\r\n\r\nPage({\r\n  data: {\r\n    // 名片信息\r\n    cardInfo: null,\r\n    // 是否为预览模式\r\n    isPreview: false,\r\n    // 加载状态\r\n    loading: true\r\n  },\r\n\r\n  onLoad(options) {\r\n    const { id, data, preview } = options;\r\n    \r\n    if (preview && data) {\r\n      // 预览模式\r\n      try {\r\n        const cardData = JSON.parse(decodeURIComponent(data));\r\n        this.setData({\r\n          cardInfo: cardData,\r\n          isPreview: true,\r\n          loading: false\r\n        });\r\n        \r\n        wx.setNavigationBarTitle({\r\n          title: '名片预览'\r\n        });\r\n      } catch (error) {\r\n        console.error('解析预览数据失败:', error);\r\n        wx.showToast({\r\n          title: '数据错误',\r\n          icon: 'none'\r\n        });\r\n        setTimeout(() => {\r\n          wx.navigateBack();\r\n        }, 1500);\r\n      }\r\n    } else if (id) {\r\n      // 详情模式\r\n      this.loadCardDetail(id);\r\n    } else {\r\n      wx.showToast({\r\n        title: '参数错误',\r\n        icon: 'none'\r\n      });\r\n      setTimeout(() => {\r\n        wx.navigateBack();\r\n      }, 1500);\r\n    }\r\n  },\r\n\r\n  // 加载名片详情\r\n  async loadCardDetail(id) {\r\n    try {\r\n      this.setData({ loading: true });\r\n\r\n      // 模拟API调用\r\n      const result = await this.mockLoadCard(id);\r\n\r\n      if (result.code === 200 && result.data) {\r\n        this.setData({\r\n          cardInfo: result.data,\r\n          loading: false\r\n        });\r\n\r\n        wx.setNavigationBarTitle({\r\n          title: result.data.name + '的名片'\r\n        });\r\n      } else {\r\n        throw new Error(result.msg || '加载失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('加载名片详情失败:', error);\r\n      this.setData({ loading: false });\r\n      \r\n      wx.showToast({\r\n        title: '加载失败，请重试',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  },\r\n\r\n  // 模拟加载名片\r\n  mockLoadCard(id) {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        resolve({\r\n          code: 200,\r\n          data: {\r\n            cardId: id, // 名片ID\r\n            company: '上海觅知房地产有限公司', // 公司名称\r\n            position: '总经理', // 职位\r\n            businessIntro: '专注于房地产开发与销售，提供专业的房产咨询服务，致力于为客户创造价值。', // 业务简介\r\n            name: '觅知君', // 姓名\r\n            gender: 1, // 性别(0-保密，1-男，2-女)\r\n            phone: '123 4567 8910', // 电话\r\n            address: '上海市浦东新区秀浦路11号', // 地址\r\n            email: '<EMAIL>', // 邮箱\r\n            website: 'www.51miz.com', // 网址\r\n            wechat: 'juezhi_123', // 微信\r\n            avatar: '/assets/images/bot-avatar.png', // 头像\r\n            images: [ // 图片\r\n              'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=Company+Image+1',\r\n              'https://via.placeholder.com/400x300/50C878/FFFFFF?text=Company+Image+2'\r\n            ],\r\n            videos: [ // 视频\r\n              'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'\r\n            ],\r\n            remark: '更新于 2024-07-20 14:30', // 备注信息\r\n            isPublic: 1, // 是否公开(0-否，1-是)\r\n            updateTime: '2024-07-20 14:30:00', // 更新时间\r\n            auditStatus: 1, // 审核状态(0-待审核，1-已通过，2-未通过)\r\n            cardType: 'business', // 名片类型\r\n            userId: '10086' // 用户ID\r\n          }\r\n        });\r\n      }, 500);\r\n    });\r\n  },\r\n\r\n  // 拨打电话\r\n  onCallPhone() {\r\n    const phone = this.data.cardInfo?.phone;\r\n    if (phone) {\r\n      wx.makePhoneCall({\r\n        phoneNumber: phone.replace(/\\s/g, '') // 移除空格\r\n      });\r\n    }\r\n  },\r\n\r\n  // 发送邮件\r\n  onSendEmail() {\r\n    const email = this.data.cardInfo?.email;\r\n    if (email) {\r\n      wx.setClipboardData({\r\n        data: email,\r\n        success: () => {\r\n          wx.showToast({\r\n            title: '邮箱已复制',\r\n            icon: 'success'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  },\r\n\r\n  // 复制微信号\r\n  onCopyWechat() {\r\n    const wechat = this.data.cardInfo?.wechat;\r\n    if (wechat) {\r\n      wx.setClipboardData({\r\n        data: wechat,\r\n        success: () => {\r\n          wx.showToast({\r\n            title: '微信号已复制',\r\n            icon: 'success'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  },\r\n\r\n  // 查看网站\r\n  onViewWebsite() {\r\n    const website = this.data.cardInfo?.website;\r\n    if (website) {\r\n      wx.setClipboardData({\r\n        data: website,\r\n        success: () => {\r\n          wx.showToast({\r\n            title: '网址已复制',\r\n            icon: 'success'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  },\r\n\r\n  // 查看地址\r\n  onViewAddress() {\r\n    const address = this.data.cardInfo?.address;\r\n    if (address) {\r\n      wx.setClipboardData({\r\n        data: address,\r\n        success: () => {\r\n          wx.showToast({\r\n            title: '地址已复制',\r\n            icon: 'success'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  },\r\n\r\n  // 保存到通讯录\r\n  onSaveContact() {\r\n    const cardInfo = this.data.cardInfo;\r\n    if (!cardInfo) return;\r\n\r\n    wx.addPhoneContact({\r\n      firstName: cardInfo.name,\r\n      organization: cardInfo.company,\r\n      title: cardInfo.position,\r\n      mobilePhoneNumber: cardInfo.phone,\r\n      email: cardInfo.email,\r\n      url: cardInfo.website,\r\n      success: () => {\r\n        wx.showToast({\r\n          title: '已保存到通讯录',\r\n          icon: 'success'\r\n        });\r\n      },\r\n      fail: () => {\r\n        wx.showToast({\r\n          title: '保存失败',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    });\r\n  },\r\n\r\n  // 分享名片\r\n  onShareCard() {\r\n    const cardInfo = this.data.cardInfo;\r\n    if (!cardInfo) return;\r\n\r\n    // 这里可以生成名片的分享链接或二维码\r\n    wx.showToast({\r\n      title: '分享功能开发中',\r\n      icon: 'none'\r\n    });\r\n  },\r\n\r\n  // 编辑名片\r\n  onEditCard() {\r\n    if (this.data.isPreview) {\r\n      wx.navigateBack();\r\n      return;\r\n    }\r\n\r\n    const cardData = encodeURIComponent(JSON.stringify(this.data.cardInfo));\r\n    wx.navigateTo({\r\n      url: `/pkg_user/pages/edit-card/edit-card?data=${cardData}`\r\n    });\r\n  },\r\n\r\n  // 预览图片\r\n  onPreviewImage(e) {\r\n    const src = e.currentTarget.dataset.src;\r\n    const images = this.data.cardInfo?.images || [];\r\n\r\n    wx.previewImage({\r\n      current: src,\r\n      urls: images\r\n    });\r\n  }\r\n});\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pkg_user/pages/card-detail/card-detail.js b/../weapp/pkg_user/pages/card-detail/card-detail.js
--- a/../weapp/pkg_user/pages/card-detail/card-detail.js	(revision 58a35da29b8c4a98a7e853c26e01da32d0ce17bc)
+++ b/../weapp/pkg_user/pages/card-detail/card-detail.js	(date 1753954500258)
@@ -1,259 +1,66 @@
 // pkg_user/pages/card-detail/card-detail.js
-const { request } = require('../../../utils/request');
-
 Page({
+
+  /**
+   * 页面的初始数据
+   */
   data: {
-    // 名片信息
-    cardInfo: null,
-    // 是否为预览模式
-    isPreview: false,
-    // 加载状态
-    loading: true
+
   },
 
+  /**
+   * 生命周期函数--监听页面加载
+   */
   onLoad(options) {
-    const { id, data, preview } = options;
-    
-    if (preview && data) {
-      // 预览模式
-      try {
-        const cardData = JSON.parse(decodeURIComponent(data));
-        this.setData({
-          cardInfo: cardData,
-          isPreview: true,
-          loading: false
-        });
-        
-        wx.setNavigationBarTitle({
-          title: '名片预览'
-        });
-      } catch (error) {
-        console.error('解析预览数据失败:', error);
-        wx.showToast({
-          title: '数据错误',
-          icon: 'none'
-        });
-        setTimeout(() => {
-          wx.navigateBack();
-        }, 1500);
-      }
-    } else if (id) {
-      // 详情模式
-      this.loadCardDetail(id);
-    } else {
-      wx.showToast({
-        title: '参数错误',
-        icon: 'none'
-      });
-      setTimeout(() => {
-        wx.navigateBack();
-      }, 1500);
-    }
+
   },
 
-  // 加载名片详情
-  async loadCardDetail(id) {
-    try {
-      this.setData({ loading: true });
+  /**
+   * 生命周期函数--监听页面初次渲染完成
+   */
+  onReady() {
 
-      // 模拟API调用
-      const result = await this.mockLoadCard(id);
-
-      if (result.code === 200 && result.data) {
-        this.setData({
-          cardInfo: result.data,
-          loading: false
-        });
-
-        wx.setNavigationBarTitle({
-          title: result.data.name + '的名片'
-        });
-      } else {
-        throw new Error(result.msg || '加载失败');
-      }
-    } catch (error) {
-      console.error('加载名片详情失败:', error);
-      this.setData({ loading: false });
-      
-      wx.showToast({
-        title: '加载失败，请重试',
-        icon: 'none'
-      });
-    }
   },
 
-  // 模拟加载名片
-  mockLoadCard(id) {
-    return new Promise((resolve) => {
-      setTimeout(() => {
-        resolve({
-          code: 200,
-          data: {
-            cardId: id, // 名片ID
-            company: '上海觅知房地产有限公司', // 公司名称
-            position: '总经理', // 职位
-            businessIntro: '专注于房地产开发与销售，提供专业的房产咨询服务，致力于为客户创造价值。', // 业务简介
-            name: '觅知君', // 姓名
-            gender: 1, // 性别(0-保密，1-男，2-女)
-            phone: '123 4567 8910', // 电话
-            address: '上海市浦东新区秀浦路11号', // 地址
-            email: '<EMAIL>', // 邮箱
-            website: 'www.51miz.com', // 网址
-            wechat: 'juezhi_123', // 微信
-            avatar: '/assets/images/bot-avatar.png', // 头像
-            images: [ // 图片
-              'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=Company+Image+1',
-              'https://via.placeholder.com/400x300/50C878/FFFFFF?text=Company+Image+2'
-            ],
-            videos: [ // 视频
-              'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
-            ],
-            remark: '更新于 2024-07-20 14:30', // 备注信息
-            isPublic: 1, // 是否公开(0-否，1-是)
-            updateTime: '2024-07-20 14:30:00', // 更新时间
-            auditStatus: 1, // 审核状态(0-待审核，1-已通过，2-未通过)
-            cardType: 'business', // 名片类型
-            userId: '10086' // 用户ID
-          }
-        });
-      }, 500);
-    });
-  },
+  /**
+   * 生命周期函数--监听页面显示
+   */
+  onShow() {
 
-  // 拨打电话
-  onCallPhone() {
-    const phone = this.data.cardInfo?.phone;
-    if (phone) {
-      wx.makePhoneCall({
-        phoneNumber: phone.replace(/\s/g, '') // 移除空格
-      });
-    }
   },
 
-  // 发送邮件
-  onSendEmail() {
-    const email = this.data.cardInfo?.email;
-    if (email) {
-      wx.setClipboardData({
-        data: email,
-        success: () => {
-          wx.showToast({
-            title: '邮箱已复制',
-            icon: 'success'
-          });
-        }
-      });
-    }
-  },
+  /**
+   * 生命周期函数--监听页面隐藏
+   */
+  onHide() {
 
-  // 复制微信号
-  onCopyWechat() {
-    const wechat = this.data.cardInfo?.wechat;
-    if (wechat) {
-      wx.setClipboardData({
-        data: wechat,
-        success: () => {
-          wx.showToast({
-            title: '微信号已复制',
-            icon: 'success'
-          });
-        }
-      });
-    }
   },
 
-  // 查看网站
-  onViewWebsite() {
-    const website = this.data.cardInfo?.website;
-    if (website) {
-      wx.setClipboardData({
-        data: website,
-        success: () => {
-          wx.showToast({
-            title: '网址已复制',
-            icon: 'success'
-          });
-        }
-      });
-    }
-  },
+  /**
+   * 生命周期函数--监听页面卸载
+   */
+  onUnload() {
 
-  // 查看地址
-  onViewAddress() {
-    const address = this.data.cardInfo?.address;
-    if (address) {
-      wx.setClipboardData({
-        data: address,
-        success: () => {
-          wx.showToast({
-            title: '地址已复制',
-            icon: 'success'
-          });
-        }
-      });
-    }
   },
 
-  // 保存到通讯录
-  onSaveContact() {
-    const cardInfo = this.data.cardInfo;
-    if (!cardInfo) return;
+  /**
+   * 页面相关事件处理函数--监听用户下拉动作
+   */
+  onPullDownRefresh() {
 
-    wx.addPhoneContact({
-      firstName: cardInfo.name,
-      organization: cardInfo.company,
-      title: cardInfo.position,
-      mobilePhoneNumber: cardInfo.phone,
-      email: cardInfo.email,
-      url: cardInfo.website,
-      success: () => {
-        wx.showToast({
-          title: '已保存到通讯录',
-          icon: 'success'
-        });
-      },
-      fail: () => {
-        wx.showToast({
-          title: '保存失败',
-          icon: 'none'
-        });
-      }
-    });
   },
-
-  // 分享名片
-  onShareCard() {
-    const cardInfo = this.data.cardInfo;
-    if (!cardInfo) return;
 
-    // 这里可以生成名片的分享链接或二维码
-    wx.showToast({
-      title: '分享功能开发中',
-      icon: 'none'
-    });
-  },
+  /**
+   * 页面上拉触底事件的处理函数
+   */
+  onReachBottom() {
 
-  // 编辑名片
-  onEditCard() {
-    if (this.data.isPreview) {
-      wx.navigateBack();
-      return;
-    }
-
-    const cardData = encodeURIComponent(JSON.stringify(this.data.cardInfo));
-    wx.navigateTo({
-      url: `/pkg_user/pages/edit-card/edit-card?data=${cardData}`
-    });
   },
 
-  // 预览图片
-  onPreviewImage(e) {
-    const src = e.currentTarget.dataset.src;
-    const images = this.data.cardInfo?.images || [];
+  /**
+   * 用户点击右上角分享
+   */
+  onShareAppMessage() {
 
-    wx.previewImage({
-      current: src,
-      urls: images
-    });
   }
-});
+})
\ No newline at end of file
