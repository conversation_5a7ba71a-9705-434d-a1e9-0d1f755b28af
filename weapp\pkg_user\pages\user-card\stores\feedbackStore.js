const { request } = require('../utils/request.js');
const FileUploadStore = require('./fileUploadStore.js');

/**
 * 提交反馈
 * @param {Object} report - 反馈/举报数据，需包含postId、content、reason等
 * @returns {Promise}
 */
const submitFeedback = (report) => {
  return request({
    url: '/blade-chat/feedback/submit',
    method: 'POST',
    data: report
  });
};

/**
 * 获取反馈历史
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise} 反馈历史列表
 */
const getFeedbackHistory = (params = {}) => {
  return request({
    url: '/blade-chat/feedback/painpoint/save',
    method: 'GET',
    data: params
  });
};

/**
 * 获取反馈详情
 * @param {string} feedbackId - 反馈ID
 * @returns {Promise} 反馈详情
 */
const getFeedbackDetail = (feedbackId) => {
  return request({
    url: `/api/feedback/detail/${feedbackId}`,
    method: 'GET'
  });
};

/**
 * 上传图片
 * @param {string} filePath - 本地文件路径
 * @returns {Promise} 上传结果
 */
const uploadImage = (filePath) => {
  return FileUploadStore.uploadFile(filePath, 'miniapp', 'feedback', null).then(result => {
    if (result.success) {
      return result.data.accessUrl || result.data.url;
          } else {
      throw new Error(result.message || '上传失败');
          }
  });
};

/**
 * 记录分享行为
 * @param {Object} data - { postId, type }
 * @returns {Promise}
 */
const recordShare = (data) => {
  return request({
    url: '/blade-chat/post/share?postId=' + data.postId + '&type=' + data.type,
    method: 'POST'
  });
};

/**
 * 分页查询反馈记录
 * @param {Object} params - { postId, current, size }
 * @returns {Promise}
 */
const getFeedbackPage = (params) => {
  return request({
    url: '/blade-chat-open/feedback/page',
    method: 'GET',
    data: params
  });
};

/**
 * 标记反馈为有帮助
 * @param {string|number} id - 反馈ID
 * @returns {Promise}
 */
const markFeedbackHelpful = (id) => {
  return request({
    url: `/blade-chat/feedback/helpful/${id}`,
    method: 'POST'
  });
};

/**
 * 提交举报
 * @param {Object} reportData - 举报数据，需包含postId、reason、content、images等
 * @returns {Promise}
 */
const submitReport = (reportData) => {
  return request({
    url: '/blade-chat/feedback/report/submit',
    method: 'POST',
    data: reportData
  });
};

module.exports = {
  submitFeedback,
  getFeedbackHistory,
  getFeedbackDetail,
  uploadImage,
  recordShare,
  getFeedbackPage,
  markFeedbackHelpful,
  submitReport
}; 