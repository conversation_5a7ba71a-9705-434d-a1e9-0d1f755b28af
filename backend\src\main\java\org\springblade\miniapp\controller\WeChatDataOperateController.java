package org.springblade.miniapp.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.miniapp.service.WeChatDataOperateService;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据操作控制器
 * 支持点赞、收藏、分享、浏览等操作
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @website duofan.top
 * @date 2025/7/31
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/data-operate")
@io.swagger.v3.oas.annotations.tags.Tag(name = "数据操作接口")
public class WeChatDataOperateController {

	private final WeChatDataOperateService operateService;

	/**
	 * 切换点赞状态
	 */
	@PostMapping("/toggle-like/{type}/{id}")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "切换点赞状态", description = "点赞/取消点赞，返回操作后的状态")
	public R<Map<String, Object>> toggleLike(
			@Parameter(description = "目标ID") @PathVariable Long id,
			@Parameter(description = "类型") @PathVariable String type) {

		boolean isLiked = operateService.toggleLike(id, type);

		Map<String, Object> result = new HashMap<>();
		result.put("success", true);
		result.put("currentState", isLiked);
		result.put("action", isLiked ? "like" : "unlike");
		result.put("message", isLiked ? "点赞成功" : "取消点赞成功");
		result.put("targetId", id);
		result.put("type", type);

		return R.data(result);
	}

	/**
	 * 切换收藏状态
	 */
	@PostMapping("/toggle-favorite/{type}/{id}")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "切换收藏状态", description = "收藏/取消收藏，返回操作后的状态")
	public R<Map<String, Object>> toggleFavorite(
			@Parameter(description = "目标ID") @PathVariable Long id,
			@Parameter(description = "类型") @PathVariable String type) {

		boolean isFavorited = operateService.toggleFavorite(id, type);

		Map<String, Object> result = new HashMap<>();
		result.put("success", true);
		result.put("currentState", isFavorited);
		result.put("action", isFavorited ? "favorite" : "unfavorite");
		result.put("message", isFavorited ? "收藏成功" : "取消收藏成功");
		result.put("targetId", id);
		result.put("type", type);

		return R.data(result);
	}

	/**
	 * 记录分享
	 */
	@PostMapping("/share/{type}/{id}")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "记录分享", description = "记录用户分享行为")
	public R<Map<String, Object>> share(
			@Parameter(description = "目标ID") @PathVariable Long id,
			@Parameter(description = "类型") @PathVariable String type) {

		boolean success = operateService.share(id, type);

		Map<String, Object> result = new HashMap<>();
		result.put("success", success);
		result.put("action", "share");
		result.put("message", success ? "分享记录成功" : "分享记录失败");
		result.put("targetId", id);
		result.put("type", type);

		return success ? R.data(result) : R.fail("分享记录失败");
	}

	/**
	 * 记录浏览
	 */
	@PostMapping("/view/{type}/{id}")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "记录浏览", description = "记录用户浏览行为")
	public R<Map<String, Object>> view(
			@Parameter(description = "目标ID") @PathVariable Long id,
			@Parameter(description = "类型") @PathVariable String type) {

		boolean success = operateService.view(id, type);

		Map<String, Object> result = new HashMap<>();
		result.put("success", success);
		result.put("action", "view");
		result.put("message", success ? "浏览记录成功" : "浏览记录失败");
		result.put("targetId", id);
		result.put("type", type);

		return success ? R.data(result) : R.fail("浏览记录失败");
	}

	/**
	 * 检查点赞状态
	 */
	@GetMapping("/check-like/{type}/{id}")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "检查点赞状态", description = "检查用户是否已点赞")
	public R<Map<String, Object>> checkLike(
			@Parameter(description = "目标ID") @PathVariable Long id,
			@Parameter(description = "类型") @PathVariable String type) {

		boolean isLiked = operateService.isLiked(id, type);

		Map<String, Object> result = new HashMap<>();
		result.put("isLiked", isLiked);

		return R.data(result);
	}

	/**
	 * 检查收藏状态
	 */
	@GetMapping("/check-favorite/{type}/{id}")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "检查收藏状态", description = "检查用户是否已收藏")
	public R<Map<String, Object>> checkFavorite(
			@Parameter(description = "目标ID") @PathVariable Long id,
			@Parameter(description = "类型") @PathVariable String type) {

		boolean isFavorited = operateService.isFavorited(id, type);

		Map<String, Object> result = new HashMap<>();
		result.put("isFavorited", isFavorited);

		return R.data(result);
	}

	// ========== 保持向后兼容的接口 ==========

	/**
	 * 点赞（兼容旧接口）
	 */
	@PostMapping("/like/{type}/{id}")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "点赞（兼容接口）", description = "建议使用 toggle-like 接口")
	@Deprecated
	public R like(@PathVariable Long id, @PathVariable String type) {
		boolean result = operateService.toggleLike(id, type);
		return R.status(result);
	}

	/**
	 * 收藏（兼容旧接口）
	 */
	@PostMapping("/favorite/{type}/{id}")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "收藏（兼容接口）", description = "建议使用 toggle-favorite 接口")
	@Deprecated
	public R favorite(@PathVariable Long id, @PathVariable String type) {
		boolean result = operateService.toggleFavorite(id, type);
		return R.status(result);
	}
}
