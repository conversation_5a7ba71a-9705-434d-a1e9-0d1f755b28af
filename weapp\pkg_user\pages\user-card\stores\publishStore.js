const { request } = require('../utils/request');
const FileUploadStore = require('./fileUploadStore');

/**
 * 发布相关的数据请求和业务逻辑
 */
class PublishStore {
  /**
   * 创建帖子
   * @param {Object} postData 帖子数据
   * @returns {Promise} 发布结果
   */
  static async createPost(postData) {
    try {
      // 构建符合接口规范的请求数据
      const requestData = {
        images: postData.images || '',
        tags: postData.tags || '',
        contactName: postData.contactName || '',
        contactType: postData.contactType || '',
        contactNumber: postData.contactNumber || '',
        content: postData.content || '',
        categoryId: postData.categoryId || 0,
        location: postData.location || '',
        address: postData.address || '',
        longitude: postData.longitude || 0,
        latitude: postData.latitude || 0
      };

      console.log('发送到接口的数据:', requestData);

      const res = await request({
        url: '/blade-chat/post/create',
        method: 'POST',
        data: requestData,
        timeout: 30000
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data,
          message: res.msg || '发布成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '发布失败',
          code: res.code
        };
      }
    } catch (error) {
      console.error('调用发布接口失败:', error);
      return {
        success: false,
        message: '网络错误，请稍后重试',
        error: error.message
      };
    }
  }

  /**
   * 保存草稿
   * @param {Object} draftData 草稿数据
   * @param {string} draftId 草稿ID（编辑时传入）
   * @returns {Promise} 保存结果
   */
  static async saveDraft(draftData, draftId = null) {
    try {
      let url = '/blade-chat/post/draft';
      let method = 'POST';

      if (draftId) {
        url = `/blade-chat/post/draft/${draftId}`;
        method = 'PUT';
      }

      const res = await request({
        url,
        method,
        data: draftData
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data,
          message: '草稿保存成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '保存失败'
        };
      }
    } catch (error) {
      console.error('保存草稿失败:', error);
      return {
        success: false,
        message: '保存失败'
      };
    }
  }

  /**
   * 加载草稿
   * @param {string} draftId 草稿ID
   * @returns {Promise} 草稿数据
   */
  static async loadDraft(draftId) {
    try {
      const res = await request({
        url: `/blade-chat/post/draft/${draftId}`,
        method: 'GET'
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data
        };
      } else {
        return {
          success: false,
          message: res.msg || '加载草稿失败'
        };
      }
    } catch (error) {
      console.error('加载草稿失败:', error);
      return {
        success: false,
        message: '加载草稿失败'
      };
    }
  }

  /**
   * 删除草稿
   * @param {string} draftId 草稿ID
   * @returns {Promise} 删除结果
   */
  static async deleteDraft(draftId) {
    if (!draftId) return { success: true };

    try {
      const res = await request({
        url: `/blade-chat/post/draft/${draftId}`,
        method: 'DELETE'
      });

      if (res.code === 200) {
        return {
          success: true,
          message: '草稿删除成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '删除失败'
        };
      }
    } catch (error) {
      console.error('删除草稿失败:', error);
      return {
        success: false,
        message: '删除失败'
      };
    }
  }

  /**
   * 获取草稿数量
   * @returns {Promise} 草稿数量
   */
  static async getDraftCount() {
    try {
      const res = await request({
        url: '/blade-chat/post/draft-list',
        method: 'GET',
        data: {
          current: 1,
          size: 1
        }
      });

      if (res.code === 200) {
        return {
          success: true,
          count: res.data.total || 0
        };
      } else {
        return {
          success: false,
          count: 0
        };
      }
    } catch (error) {
      console.error('获取草稿数量失败:', error);
      return {
        success: false,
        count: 0
      };
    }
  }

  /**
   * 加载分类列表
   * @returns {Promise} 分类列表
   */
  static async loadCategories() {
    try {
      const res = await request({
        url: '/blade-chat/category/list',
        method: 'GET'
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data.records || []
        };
      } else {
        return {
          success: false,
          message: res.msg || '加载分类失败'
        };
      }
    } catch (error) {
      console.error('加载分类失败:', error);
      return {
        success: false,
        message: '加载分类失败'
      };
    }
  }

  /**
   * 根据分类获取标签
   * @param {string} categoryId 分类ID
   * @returns {Promise} 标签列表
   */
  static async getTagsByCategory(categoryId) {
    try {
      const res = await request({
        url: `/blade-chat/post/tag/category/${categoryId}`,
        method: 'GET'
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data || []
        };
      } else {
        return {
          success: false,
          message: res.msg || '加载标签失败'
        };
      }
    } catch (error) {
      console.error('根据分类获取标签失败:', error);
      return {
        success: false,
        message: '加载标签失败'
      };
    }
  }

  /**
   * 获取所有标签列表
   * @param {Object} params 查询参数
   * @returns {Promise} 标签列表
   */
  static async getTagList(params = {}) {
    try {
      const res = await request({
        url: '/blade-chat/tag/list',
        method: 'GET',
        data: params
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data.records || [],
          total: res.data.total || 0
        };
      } else {
        return {
          success: false,
          message: res.msg || '加载标签失败'
        };
      }
    } catch (error) {
      console.error('获取标签列表失败:', error);
      return {
        success: false,
        message: '加载标签失败'
      };
    }
  }

  /**
   * 创建自定义标签
   * @param {Object} tagData 标签数据
   * @returns {Promise} 创建结果
   */
  static async createCustomTag(tagData) {
    try {
      const res = await request({
        url: '/blade-chat/post/tag/custom',
        method: 'POST',
        data: tagData
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data,
          message: '标签创建成功'
        };
      } else {
        return {
          success: false,
          message: res.msg || '创建标签失败'
        };
      }
    } catch (error) {
      console.error('创建自定义标签失败:', error);
      return {
        success: false,
        message: '创建标签失败'
      };
    }
  }

  /**
   * 上传图片到服务器
   * @param {Array} images 图片数组
   * @param {string} businessType 业务类型
   * @param {string|number} businessId 业务ID
   * @returns {Promise} 上传结果
   */
  static async uploadImages(images, businessType = 'post', businessId = null) {
    if (!images || images.length === 0) {
      return { success: true, data: [] };
    }

    try {
      // 过滤出需要上传的临时图片
      const tempImages = images.filter(image => image.isTemp);
      
      if (tempImages.length === 0) {
        // 如果没有临时图片，直接返回现有图片数据
        return {
          success: true,
          data: images.map(image => ({
            fileId: image.fileId || image.id,
            url: image.url || image.path,
            size: image.size || 0,
            type: image.type || 'image/jpeg',
            name: image.name || `image_${Date.now()}.jpg`
          }))
        };
      }

      // 上传临时图片
      const uploadPromises = tempImages.map(async (image) => {
        try {
          const result = await FileUploadStore.uploadFile(
            image.path,
            'miniapp',
            businessType,
            businessId
          );
          
          if (result.success) {
            return {
              fileId: result.data.id || result.data.fileId,
              url: result.data.accessUrl || result.data.path,
              size: image.size || 0,
              type: image.type || 'image/jpeg',
              name: result.data.name || `image_${Date.now()}.jpg`
            };
          } else {
            throw new Error(result.message);
          }
        } catch (error) {
          console.error('上传图片失败:', error);
          throw error;
        }
      });

      const uploadedImages = await Promise.all(uploadPromises);
      
      // 合并已上传的图片和现有图片
      const existingImages = images.filter(image => !image.isTemp).map(image => ({
        fileId: image.fileId || image.id,
        url: image.url || image.path,
        size: image.size || 0,
        type: image.type || 'image/jpeg',
        name: image.name || `image_${Date.now()}.jpg`
      }));

      return {
        success: true,
        data: [...existingImages, ...uploadedImages]
      };
    } catch (error) {
      console.error('批量上传图片失败:', error);
      return {
        success: false,
        message: '图片上传失败'
      };
    }
  }

  /**
   * 删除服务器文件
   * @param {string|Array} fileIds 文件ID或ID数组
   * @returns {Promise} 删除结果
   */
  static async deleteServerFile(fileIds) {
    try {
      const result = await FileUploadStore.removeFile(fileIds);
      return result;
    } catch (error) {
      console.error('删除服务器文件失败:', error);
      return { 
        success: false, 
        message: '删除文件失败' 
      };
    }
  }

  /**
   * 获取位置信息
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   * @returns {Promise} 位置信息
   */
  static async getLocationInfo(latitude, longitude) {
    try {
      const res = await request({
        url: '/blade-chat/location/info',
        method: 'GET',
        data: { latitude, longitude }
      });

      if (res.code === 200) {
        return {
          success: true,
          data: res.data
        };
      } else {
        return {
          success: false,
          message: res.msg || '获取位置信息失败'
        };
      }
    } catch (error) {
      console.error('获取位置信息失败:', error);
      return {
        success: false,
        message: '获取位置信息失败'
      };
    }
  }
}

module.exports = PublishStore; 