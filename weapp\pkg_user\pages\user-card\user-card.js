Page({
  data: {
    userCards: [
      {
        cardId: 'CARD001', // 名片ID
        company: '上海觅知房地产有限公司', // 公司名称
        position: '总经理', // 职位
        businessIntro: '专注于房地产开发与销售，提供专业的房产咨询服务，致力于为客户创造价值。', // 业务简介
        name: '觅知君', // 姓名
        gender: 1, // 性别(0-保密，1-男，2-女)
        phone: '123 4567 8910', // 电话
        address: '上海市浦东新区秀浦路11号', // 地址
        email: '<EMAIL>', // 邮箱
        website: 'www.51miz.com', // 网址
        wechat: 'mizhi001', // 微信
        avatar: '/assets/images/def-avatar.png', // 头像
        logo: '/assets/images/bot-avatar.png', // 公司logo
        qrCode: '/assets/images/bot-avatar.png', // 二维码
        images: ['https://via.placeholder.com/400x300'], // 图片
        videos: ['sample_video.mp4'], // 视频
        remark: '房地产行业资深从业者', // 备注信息
        isPublic: 1, // 是否公开(0-否，1-是)
        updateTime: '2024-01-20 14:30', // 更新时间
        auditStatus: 1, // 审核状态(0-待审核，1-已通过，2-未通过)
        cardType: '房地产' // 名片类型
      },
      {
        cardId: 'CARD002',
        company: '北京科技创新有限公司',
        position: '技术总监',
        businessIntro: '专注于人工智能和大数据技术研发，为企业提供智能化解决方案。',
        name: '李明',
        gender: 1,
        phone: '138 0000 1234',
        address: '北京市朝阳区建国路88号SOHO现代城',
        email: '<EMAIL>',
        website: 'www.techinnov.com',
        wechat: 'liming_tech',
        avatar: '/assets/images/def-avatar.png',
        logo: '/assets/images/bot-avatar.png',
        qrCode: '/assets/images/bot-avatar.png',
        images: ['https://via.placeholder.com/400x300'],
        videos: ['tech_demo.mp4'],
        remark: '技术创新引领者',
        isPublic: 1,
        updateTime: '2024-01-19 16:45',
        auditStatus: 1,
        cardType: '科技'
      },
      {
        cardId: 'CARD003',
        company: '深圳美食文化传播有限公司',
        position: '行政总厨',
        businessIntro: '传承中华美食文化，创新现代烹饪技艺，为客户提供高品质餐饮服务。',
        name: '王美食',
        gender: 2,
        phone: '159 8888 6666',
        address: '深圳市南山区科技园南区深圳湾科技生态园',
        email: '<EMAIL>',
        website: 'www.deliciousfood.com',
        wechat: 'chef_wang',
        avatar: '/assets/images/def-avatar.png',
        logo: '/assets/images/bot-avatar.png',
        qrCode: '/assets/images/bot-avatar.png',
        images: ['https://via.placeholder.com/400x300'],
        videos: ['cooking_show.mp4'],
        remark: '美食艺术家',
        isPublic: 1,
        updateTime: '2024-01-18 10:20',
        auditStatus: 1,
        cardType: '餐饮'
      },
      {
        cardId: 'CARD004',
        company: '广州金融投资集团',
        position: '投资经理',
        businessIntro: '专业从事股权投资和资产管理，为客户提供全方位的财富管理服务。',
        name: '陈投资',
        gender: 1,
        phone: '186 6666 8888',
        address: '广州市天河区珠江新城花城大道85号',
        email: '<EMAIL>',
        website: 'www.gzfinance.com',
        wechat: 'chen_invest',
        avatar: '/assets/images/def-avatar.png',
        logo: '/assets/images/bot-avatar.png',
        qrCode: '/assets/images/bot-avatar.png',
        images: ['https://via.placeholder.com/400x300'],
        videos: ['investment_intro.mp4'],
        remark: '财富管理专家',
        isPublic: 0,
        updateTime: '2024-01-17 09:15',
        auditStatus: 2,
        cardType: '金融'
      },
      {
        cardId: 'CARD005',
        company: '杭州教育科技有限公司',
        position: '教育总监',
        businessIntro: '致力于在线教育平台开发，为学生提供优质的学习资源和个性化教学服务。',
        name: '张教育',
        gender: 2,
        phone: '177 7777 9999',
        address: '杭州市西湖区文三路259号昌地火炬大厦',
        email: '<EMAIL>',
        website: 'www.hzedu.com',
        wechat: 'zhang_edu',
        avatar: '/assets/images/def-avatar.png',
        logo: '/assets/images/bot-avatar.png',
        qrCode: '/assets/images/bot-avatar.png',
        images: ['https://via.placeholder.com/400x300'],
        videos: ['education_demo.mp4'],
        remark: '教育创新践行者',
        isPublic: 1,
        updateTime: '2024-01-16 14:00',
        auditStatus: 0,
        cardType: '教育'
      },
      {
        cardId: 'CARD006',
        company: '成都医疗健康科技有限公司',
        position: '医疗总监',
        businessIntro: '专注于智慧医疗解决方案，结合AI技术提升医疗服务质量和效率。',
        name: '刘医生',
        gender: 1,
        phone: '199 0000 5555',
        address: '成都市高新区天府大道中段1388号',
        email: '<EMAIL>',
        website: 'www.cdmedical.com',
        wechat: 'dr_liu',
        avatar: '/assets/images/def-avatar.png',
        logo: '/assets/images/bot-avatar.png',
        qrCode: '/assets/images/bot-avatar.png',
        images: ['https://via.placeholder.com/400x300'],
        videos: ['medical_tech.mp4'],
        remark: '智慧医疗先行者',
        isPublic: 1,
        updateTime: '2024-01-15 11:30',
        auditStatus: 1,
        cardType: '医疗'
      }
    ]
  },

  onLoad() {
    console.log('用户卡片页面加载', this.data.userCards);
  },

  // 处理添加名片事件
  onAddCard(e) {
    const newCard = e.detail;
    console.log('添加新名片:', newCard);
    
    // 将新名片添加到列表中
    const userCards = [...this.data.userCards, newCard];
    this.setData({
      userCards: userCards
    });
    
    console.log('名片列表已更新，总数:', userCards.length);
  }
});
