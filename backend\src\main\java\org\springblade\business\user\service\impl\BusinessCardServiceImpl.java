/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.mapper.SupPostMapper;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.business.user.mapper.BusinessCardMapper;
import org.springblade.business.user.service.IBusinessCardService;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.exception.SecureException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.miniapp.service.WeChatPostService;
import org.springblade.miniapp.utils.IpUtils;
import org.springblade.modules.system.service.IParamService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 名片信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
public class BusinessCardServiceImpl extends BaseServiceImpl<BusinessCardMapper, BusinessCard> implements IBusinessCardService {
	@Resource
	private IParamService paramService;
	@Resource
	private SupPostMapper supPostMapper;
	@Override
	public IPage<BusinessCardVO> selectBusinessCardPage(IPage<BusinessCardVO> page, BusinessCardVO businessCard) {
		if(AuthUtil.isAdministrator()){
			return page.setRecords(baseMapper.selectBusinessCardPage(page, businessCard));
		}
		if(getEnableAudit()){
			businessCard.setAuditStatus(1);
		}
		return page.setRecords(baseMapper.selectBusinessCardPage(page, businessCard));
	}


	@Override
	 public IPage<BusinessCardVO> getHomeCartList(Query query, BusinessCardVO businessCard){
		//判断是否未登录且页码大于5页
		Long userId = AuthUtil.getUserId();
		if ((userId == null || userId == -1L) && query.getCurrent() > 5) {
			throw new SecureException("登录访问更多数据");
		}
		Map<String, Object> params = new HashMap<>();
		if (getEnableAudit()) {
			params.put("auditStatus", 1);
		}
		if (businessCard.getLatitude() != null && businessCard.getLongitude() != null ) {
			params.put("latitude", businessCard.getLatitude());
			params.put("longitude", businessCard.getLongitude());
		}
		//如果范围大于50km就报错
		if (businessCard.getScope() != null) {
			if (businessCard.getScope() > 50){
				throw new ServiceException("范围不能大于50km");
			}else if (businessCard.getScope() < 0){
				throw new ServiceException("范围不能小于0");
			}
			params.put("scope", businessCard.getScope());
		}else {
			params.put("scope", 50);  // 默认50
		}
		Map<String, Object> mtp = BeanUtil.beanToMap(businessCard, false, true);
		params.putAll(mtp);
//		params.remove("status"); // 移除 postVO 里的 status
		params.put("status", 1); // 再 put 1
		params.put("createUser", AuthUtil.getUserId());
		params.put("visitUser", AuthUtil.getUserId());
		IPage<BusinessCardVO> supCardVOIPage = baseMapper.selectCardList(Condition.getPage(query), params);
		// 处理每个VO的content字段
	/*	for (SupPostVO vo : supPostVOIPage.getRecords()) {
			vo.setContent(getDisplayContent(vo.getContent()));
			vo.setCreateUser(null);
			vo.setContactType(null);
			vo.setContactPhone(null);
			vo.setContactName(null);
			vo.setContactPhone(null);
			vo.setUpdateUser( null);
		}*/
		return supCardVOIPage;
	 }

	@Resource
	private HttpServletRequest request;
	@Override
	@Transactional
	public BusinessCardVO getDetail(BusinessCardVO businessCard) {
		List< BusinessCardVO>list =baseMapper.selectBusinessCardPage(Page.of(1, 1), businessCard);
		if(!list.isEmpty()){
			String ipAddr = IpUtils.getIpAddr(request);
			supPostMapper.insertViewHistory(Map.of("relevancyId",list.get(0).getId(),"userId", AuthUtil.getUserId(),"ip",ipAddr,"type",1));
		}
		return list.get(0);
	}




	private boolean getEnableAudit() {
		return "true".equals(paramService.getByKey("card.preAudit.enabled"));
	}
}
