package org.springblade.miniapp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.business.config.service.IFormConfigService;
import org.springblade.business.post.entity.*;
import org.springblade.business.post.mapper.*;
import org.springblade.business.post.request.PostCreateRequest;
import org.springblade.business.post.service.ISupPostService;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.post.wrapper.PostWrapper;
import org.springblade.business.user.mapper.WeUserMapper;
import org.springblade.common.constant.bizz.ContactTypeEnum;
import org.springblade.common.constant.bizz.PostBusinessType;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.business.user.mapper.CategoryTagMapper;
import org.springblade.business.user.mapper.SupPostMapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.exception.SecureException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.miniapp.command.TagCreateCommand;
import org.springblade.common.constant.bizz.MessageTypeEnum;
import org.springblade.miniapp.service.WeChatMessageService;
import org.springblade.miniapp.service.WeChatPostService;
import org.springblade.miniapp.utils.EntityUtils;
import org.springblade.miniapp.utils.IpUtils;
import org.springblade.modules.system.service.IParamService;
import org.springblade.modules.system.service.impl.UserServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.common.cache.CacheNames.WECHAT_CATEGORY_TAGS;

@Slf4j
@Service
public class WeChatPostServiceImpl implements WeChatPostService {

	@Resource
	private SupPostMapper supPostMapper;
	@Resource
	private ISupPostService postService;
	@Resource
	private IParamService paramService;
	@Resource
	private CategoryTagMapper categoryTagMapper;

	@Resource
	private WeChatMessageService messageService;

	@Resource
	 private PostCarpoolMapper carpoolMapper;

	@Resource
	private UrbJobSeekingMapper urbJobSeekingMapper;

	@Resource
	private UrbJobOfferMapper urbJobOfferMapper;

	@Resource
	private IFormConfigService formConfigService;

	@Resource
	private RentHouseMapper rentHouseMapper;

	@Resource
	private UsedCarMapper usedCarMapper;

	private static final int MAX_DISPLAY_LENGTH = 80;
	@Autowired
	private UserServiceImpl userServiceImpl;

	@Resource
	private WeUserMapper weUserMapper;


	@Override
	public IPage<SupPostVO> getMyPosts(Query query) {
		return supPostMapper.selectMyPosts(Condition.getPage(query),
			Map.of("createUser", AuthUtil.getUserId(),
				"visitUser", AuthUtil.getUserId()));
	}

	@Override
	public IPage<SupPostVO> getLikedPosts(Query query) {
		return supPostMapper.selectPostList(Condition.getPage(query), Map.of(
			"createUser", AuthUtil.getUserId(),
			"visitUser", AuthUtil.getUserId(),
			"liked", true));
	}

	@Override
	public IPage<SupPostVO> getFavoritePosts(Query query) {
		return supPostMapper.selectPostList(Condition.getPage(query),
			Map.of(
				"createUser", AuthUtil.getUserId(),
				"visitUser", AuthUtil.getUserId(),
				"favorited", true));
	}

	@Override
	public IPage<SupPostVO> getViewHistory(Query query) {
		return supPostMapper.selectViewHistoryList(Condition.getPage(query),
			Map.of("createUser", AuthUtil.getUserId(),
				"visitUser", AuthUtil.getUserId(),
				"viewed", true));
	}

	@Override
	public boolean clearViewHistory() {
		return supPostMapper.clearViewHistory(MapUtil.of("createUser", AuthUtil.getUserId())) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean deleteViewHistory(Long id) {
		return supPostMapper.deleteViewHistory(
			Map.of(
				"id", id,
				"userId", AuthUtil.getUserId())) > 0;
	}

	@Resource
	private HttpServletRequest request;


	@Override
	public boolean addViewHistory(Long postId) {
		String ipAddr = IpUtils.getIpAddr(request);
		return supPostMapper.insertViewHistory(
			Map.of("postId",postId, "userId", AuthUtil.getUserId(),"ip",ipAddr,"type",0)) > 0;
	}

	@Override
	public IPage<SupPostVO> getCallHistory(Query query) {
		return supPostMapper.selectCallHistory(Condition.getPage(query),
			MapUtil.of("createUser", AuthUtil.getUserId()));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean clearCallHistory() {
		return supPostMapper.clearCallHistory(
			MapUtil.of("createUser", AuthUtil.getUserId())) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean addCallHistory(Long postId) {
		return supPostMapper.insertCallHistory(
			Map.of("postId", postId, "createUser", AuthUtil.getUserId())) > 0;
	}

	@Override
	@Transactional
	public SupPostVO getPostDetail(Long id) {
		// 获取帖子详情
		SupPostVO post = supPostMapper.selectPostDetail(
			Map.of("id", id, "visitUser", AuthUtil.getUserId()));
		if (post == null) {
			throw new ServiceException("帖子不存在");
		}
		//把帖子的联系人名和联系方式进行一个加密
		String contactName = post.getContactName();
		String contactNumber = post.getContactPhone();
		String contactType = post.getContactType();

		if ("1".equals(post.getIsAnonymity())) {
			post.setCreateUser(null);
		}

		//post.setContactName(maskName(contactName));
		post.setContactPhone(maskContact(contactNumber, contactType));

		// 添加浏览记录
		this.addViewHistory(id);
		return post;
	}

	/**
	 * 姓名脱敏
	 */
	public static String maskName(String name) {
		if (name == null || name.isEmpty()) return "";
		int len = name.length();
		if (len == 2) {
			return name.charAt(0) + "*";
		} else if (len == 3) {
			return name.charAt(0) + "*" + name.charAt(2);
		} else if (len > 3) {
			// 超过3个字，只保留首尾，中间用一个*代替
			return name.charAt(0) + "*" + name.charAt(len - 1);
		}
		return name;
	}

	/**
	 * 联系方式脱敏
	 */
	public static String maskContact(String contact, String type) {
		if (contact == null) return "";
		if (ContactTypeEnum.WECHAT.getCode().equalsIgnoreCase(type)) {
			return "******";
		} else if (ContactTypeEnum.PHONE.getCode().equalsIgnoreCase(type) && contact.length() >= 7) {
			// 手机号中间4位用一个*代替
			return contact.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
		}
		return contact;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean submitPost(SupPostVO post) {
		post.setCreateUser(AuthUtil.getUserId());
		post.setPublishTime(LocalDateTime.now().toString());
		post.setStatus(1);
		post.setIsDeleted(0);
		post.setCompleted(0); // 默认未完成

		boolean isSaved = postService.save(post);

		// 先假数据
		log.warn("post.getId() = {}", post.getId());

		// 添加分类标签
		if (isSaved) {
			supPostMapper.savePostCategory(
				Map.of("postId", post.getId(), "categoryId", post.getCategoryId()));
		}
		return isSaved;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long saveDraft(SupPostVO post) {
		// 判断是不是存在的草稿重新保存
		if (post.getId() != null) {
			// 删除原有的草稿
			supPostMapper.replyDraft(
				Map.of("id", post.getId(), "createUser", AuthUtil.getUserId()));
		}
		post.setCreateUser(AuthUtil.getUserId());
		post.setStatus(0); // 草稿状态
		post.setIsDeleted(0);
		post.setCompleted(0); // 默认未完成
		postService.saveOrUpdate(post);
		// 回复删除了的草稿
		if (post.getId() == null) {
			return null;
		}
		return post.getId();
	}

	@Override
	public IPage<SupPostVO> getDraftList(Query query) {
		return supPostMapper.selectDraftList(Condition.getPage(query),
			Map.of("createUser", AuthUtil.getUserId(),
				"status", 0));
	}

	@Override
	public SupPostVO getDraftDetail(Long id) {
		return supPostMapper.selectDraftDetail(
			Map.of("id", id, "createUser", AuthUtil.getUserId()));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean deleteDraft(Long id) {
		return postService.remove(EntityUtils.getDataScopeWrapper(SupPost.class, id, AuthUtil.getUserId()));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean deletePost(Long id) {
		//判断帖子类型
		SupPost post = postService.getById(id);
		//确保帖子是自己的
		if (post == null || !post.getCreateUser().equals(AuthUtil.getUserId())) {
			return false;
		}
		//如果是顺风车帖子同时删除顺风车帖子表
		if (post.getBusinessType().equals("carpool")) {
			// 删除拼车帖子
			 carpoolMapper.deleteByPostId(id);
		}
		//如果是求工作帖子同时删除求工作帖子表
		if (post.getBusinessType().equals("jobSeeking")) {
			// 删除求工作帖子
			 urbJobSeekingMapper.deleteByPostId(id);
		}
		//如果是求工作帖子同时删除求工作帖子表
		if (post.getBusinessType().equals("jobOffer")) {
			// 删除求工作帖子
			 urbJobOfferMapper.deleteByPostId(id);
		}
		// 确保只能删除自己的帖子表
		return postService.remove(EntityUtils.getDataScopeWrapper(SupPost.class, id, AuthUtil.getUserId()));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean toggleCompleted(Long id) {
		// 获取当前帖子
		SupPost post = postService.getById(id);
		if (post == null || !post.getCreateUser().equals(AuthUtil.getUserId())) {
			return false;
		}

		// 切换完成状态
		SupPost updatePost = new SupPost();
		updatePost.setId(id);
		updatePost.setCompleted(post.getCompleted() == 1 ? 0 : 1);

		// 更新帖子
		return postService.updateById(updatePost);
	}



	@Override
	public IPage<SupPostVO> getHomePostList(Query query, SupPostVO postVO) {
		//判断是否未登录且页码大于5页
		Long userId = AuthUtil.getUserId();
		if ((userId == null || userId == -1L) && query.getCurrent() > 5) {
			throw new SecureException("登录访问更多数据");
		}
		//没有登录不允许进行内容模糊查询
		String content = postVO.getContent();
		// 只要有内容搜索，必须登录
		if (content != null && !content.trim().isEmpty() && (userId == null || userId == -1L)) {
			throw new SecureException("请先登录后再进行内容搜索");
		}
		Map<String, Object> params = new HashMap<>();
		if (isOpenPreAudit()) {
			params.put("auditStatus", 1);
		}
		if (postVO.getLatitude() != null && postVO.getLongitude() != null ) {
			params.put("latitude", postVO.getLatitude());
			params.put("longitude", postVO.getLongitude());
		}
		if(postVO.getBusinessType() != null){
			params.put("businessType", postVO.getBusinessType());
		}
		//如果范围大于50km就报错
		if (postVO.getScope() != null) {
			if (postVO.getScope() > 50){
				throw new ServiceException("范围不能大于50km");
			}else if (postVO.getScope() < 0){
				throw new ServiceException("范围不能小于0");
			}
			params.put("scope", postVO.getScope());
		}else {
			params.put("scope", 50);  // 默认50
		}
		Map<String, Object> mtp = BeanUtil.beanToMap(postVO, false, true);
		params.putAll(mtp);
//		params.remove("status"); // 移除 postVO 里的 status
		params.put("status", 1); // 再 put 1
		params.put("createUser", AuthUtil.getUserId());
		params.put("visitUser", AuthUtil.getUserId());
		IPage<SupPostVO> supPostVOIPage = supPostMapper.selectPostList(Condition.getPage(query), params);
		// 处理每个VO的content字段
		for (SupPostVO vo : supPostVOIPage.getRecords()) {
			vo.setContent(getDisplayContent(vo.getContent()));
			vo.setCreateUser(null);
			vo.setContactType(null);
			vo.setContactPhone(null);
			vo.setContactName(null);
			vo.setContactPhone(null);
			vo.setUpdateUser( null);
		}
		return supPostVOIPage;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public SupPost createPost(PostCreateRequest createRequest) {

		SupPost post = new SupPost();
		String content = createRequest.getContent();
		post.setContent(content != null ? content.trim() : null);  //内容
		String images = createRequest.getImages();
		post.setImages(images != null ? images.trim() : null);  // 图片
		//前端传入的是一个逗号分割的String，转化为List
		if (createRequest.getTags() != null) {
			// 使用逗号分割字符串并转换为列表，同时过滤空字符串
			post.setTags(Arrays.stream(createRequest.getTags().split(","))
				.map(String::trim)
				.filter(tag -> !tag.isEmpty())
				.collect(Collectors.toList()));
		}
		String contactName = createRequest.getContactName();
		post.setContactName(contactName != null ? contactName.trim() : null);// 联系人姓名
		String contactType = createRequest.getContactType();
		post.setContactType(contactType != null ? contactType.trim() : null); // 联系信息类型
		String contactNumber = createRequest.getContactNumber();
		post.setContactPhone(contactNumber != null ? contactNumber.trim() : null); //联系编号
		post.setCategoryId(createRequest.getCategoryId());  // 分类ID
		String location = createRequest.getLocation();
		post.setLocation(location != null ? location.trim() : null);  // 地理位置
		String address = createRequest.getAddress();
		post.setAddress(address != null ? address.trim() : null); // 详细地址
		post.setPublishTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));//发布时间
		//凭借经纬度(String) 为geoLocation字段
		JSONObject geo = new JSONObject();
		String loc = createRequest.getLocation();
		geo.put("location", loc != null ? loc.trim() : null);
		String addr = createRequest.getAddress();
		geo.put("address", addr != null ? addr.trim() : null);
		geo.put("longitude", createRequest.getLongitude());
		geo.put("latitude", createRequest.getLatitude());
		post.setGeoLocation(geo.toString());
		post.setLongitude(createRequest.getLongitude()); // 经度
		post.setLatitude(createRequest.getLatitude()); // 纬度
		post.setCreateUser(AuthUtil.getUserId());
		post.setIsAnonymity(createRequest.getIsAnonymity());// 是否匿名

//		// 1. 获取前端传入的公司名和手机号
//		String companyName = createRequest.getJobOffer().getCompanyName();
//		String mobile = createRequest.getContactNumber();
//		Long userId = null;
//
//		if (mobile != null) {
//			if (companyName != null) {
//				companyName = companyName.trim();
//			}
//			mobile = mobile.trim();
//			// 2. 先查用户表是否有手机号的用户
//			WeUser existUser = weUserMapper.selectOne(
//				new QueryWrapper<WeUser>()
//					.eq("mobile", mobile)
//					.eq("company_name", companyName)
//			);
//			if (existUser != null) {
//				// 3. 已存在，直接用该用户
//				userId = existUser.getId();
//			} else {
//				// 4. 不存在，创建新用户
//				WeUser weUser = new WeUser();
//				weUser.setCompanyName(companyName);
//				weUser.setMobile(mobile);
//				weUser.setNickname(createRequest.getContactName());
//				weUserMapper.insert(weUser);
//				userId = weUser.getId();
//			}
//		}
//
//// 判断帖子类型
//		post.setBusinessType(createRequest.getBusinessType());
//		if (userId != null) {
//			post.setCreateUser(userId);
//			post.setContactName(createRequest.getContactName());
//			post.setContactNumber(mobile);
//		}

		// 判断帖子类型
		post.setBusinessType(createRequest.getBusinessType());
		//机构id
		if (createRequest.getInstitutionId() != null){
			post.setInstitutionId(createRequest.getInstitutionId());
		}
		postService.save(post);
		// 如果是顺风车帖子，插入顺风车表
		if (PostBusinessType.CARPOOL.getValue().equalsIgnoreCase(createRequest.getBusinessType())) {
			PostCarpool carpool = new PostCarpool();
			BeanUtil.copyProperties(createRequest.getCarpool(), carpool);
			carpool.setPostId(post.getId());
			carpoolMapper.insert(carpool);
		}
		//如果是求职帖子，插入求职表
		if (PostBusinessType.JOB_SEEKING.getValue().equalsIgnoreCase(createRequest.getBusinessType())) {
			JobSeeking jobSeek = new JobSeeking();
			BeanUtil.copyProperties(createRequest.getJobSeek(), jobSeek);
			jobSeek.setPostId(post.getId());
			urbJobSeekingMapper.insert(jobSeek);
		}
		//如果是招聘帖子，插入招聘表
		if (PostBusinessType.JOB_OFFER.getValue().equalsIgnoreCase(createRequest.getBusinessType())) {
			JobOffer jobOffer = new JobOffer();
			BeanUtil.copyProperties(createRequest.getJobOffer(), jobOffer);
			jobOffer.setPostId(post.getId());
			urbJobOfferMapper.insert(jobOffer);
		}
		//如果是租房帖子，插入租房表
		if (PostBusinessType.RENT_HOUSE.getValue().equalsIgnoreCase(createRequest.getBusinessType())) {
			RentHouse rentHouse = new RentHouse();
			BeanUtil.copyProperties(createRequest.getRentHouse(), rentHouse);
			rentHouse.setPostId(post.getId());
			rentHouseMapper.insert(rentHouse);
		}
		//如果是二手车帖子，插入二手车表
		if (PostBusinessType.USED_CAR.getValue().equalsIgnoreCase(createRequest.getBusinessType())) {
			UsedCar usedCar = new UsedCar();
			BeanUtil.copyProperties(createRequest.getUsedCar(), usedCar);
			usedCar.setPostId(post.getId());
			usedCarMapper.insert(usedCar);
		}
		// 新增：发消息通知
		messageService.createMessage(
			post.getCreateUser(),
			MessageTypeEnum.POST_ADD,
			Map.of("postTitle", "新帖发布成功"),
			post.getId()
		);
		return post;
	}

	@Override
	public Long countPost(String location) {
		QueryWrapper<SupPost> queryWrapper = new QueryWrapper<SupPost>();
		if (location != null) {
			queryWrapper.like("location", location);
		}

		return supPostMapper.selectCount(queryWrapper);

	}

	@Override
	public Long countView(String location) {
		return supPostMapper.countView(location);
	}

	@Override
	public Long countShare(String location) {
		return supPostMapper.countShare(location);
	}

	@Override
	public boolean addShareHistory(Long postId) {
		return supPostMapper.insertShareHistory(
			Map.of("postId", postId, "UserId", AuthUtil.getUserId())) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean recordShare(Long postId, String type) {
		// 这里可以根据业务需求插入一条分享记录到数据库
		// 示例：直接调用已有的addShareHistory方法（如需区分type可扩展表结构）
		return this.addShareHistory(postId);
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public String getContact(Long id) {
		//查询该用户访问次数
		LocalDate today = LocalDate.now();
		Integer cot = supPostMapper.getUserContactAccessCount(Map.of("userId", AuthUtil.getUserId(), "postId", id));
		// 使用 Objects.requireNonNullElse 设置默认值
		int accessCot = Objects.requireNonNullElse(cot, 0);
		//判断该帖子是否被访问
		if (accessCot == 0) {
			Integer count = supPostMapper.getUserContactAccessCount(Map.of("userId", AuthUtil.getUserId(), "today", today.toString()));
			// 使用 Objects.requireNonNullElse 设置默认值
			int accessCount = Objects.requireNonNullElse(count, 0);
			int limit = getContactAccessCount();
			if (accessCount >= limit) {
				throw new ServiceException("您的联系方式访问权限已上限");
			}
		}
		SupPost post = postService.getById(id);
		if (post != null) {
			if (accessCot == 0) {
				supPostMapper.insertContactAccess(Map.of("userId", AuthUtil.getUserId(), "postId", id));
			}
			return post.getContactType() + "," +post.getContactPhone();
		}
		return null;
	}

	@Override
	public Boolean moveToDraft(Long id) {
		return null;
	}

	@Override
	public Boolean publishFromDraft(Long id) {
		return null;
	}


	@Override
	@Cacheable(cacheNames = WECHAT_CATEGORY_TAGS, key = "#id")
	public List<Tag> getTagsByCategory(Long id) {
		return categoryTagMapper.selectTagsByCategoryId(id);
	}

	/**
	 * 标签结构设计  不支持用户自定义标签，也不需要
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public Boolean createCustomTag(TagCreateCommand request) {

		return true;
	}

	/**
	 * 根据分类ID分页获取帖子列表
	 *
	 * @param postCategoryId 分类ID
	 * @param query          查询参数
	 * @return 帖子列表
	 */
	@Override
	public IPage<SupPostVO> getPostListByCategoryId(Long postCategoryId, Query query) {
		IPage<SupPost> postPage = supPostMapper.getByCategoryId(
			Condition.getPage(query), // 用 Condition.getPage(query) 替换 query.getPage()
			postCategoryId
		);
		return PostWrapper.build().pageVO(postPage);

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean toggleLike(Long postId) {
		Map<String, Object> params = new HashMap<>();
		params.put("postId", postId);
		params.put("createUser", AuthUtil.getUserId());

		// 检查用户是否已经点赞
		int liked = supPostMapper.checkUserLiked(params);
		if (liked > 0) {
			// 已点赞，取消点赞
			return supPostMapper.deleteLike(params) > 0;
		} else {
			// 未点赞，添加点赞
			return supPostMapper.insertLike(params) > 0;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean toggleFavorite(Long postId) {
		Map<String, Object> params = new HashMap<>();
		params.put("postId", postId);
		params.put("createUser", AuthUtil.getUserId());

		// 检查用户是否已经收藏
		int favorited = supPostMapper.checkUserFavorited(params);
		if (favorited > 0) {
			// 已收藏，取消收藏
			return supPostMapper.deleteFavorite(params) > 0;
		} else {
			// 未收藏，添加收藏
			return supPostMapper.insertFavorite(params) > 0;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean clearFavorites() {
		Map<String, Object> params = new HashMap<>();
		params.put("createUser", AuthUtil.getUserId());

		// 清空当前用户的所有收藏
		return supPostMapper.clearFavorites(params) > 0;
	}

	private boolean isOpenPreAudit() {
		String value = paramService.getByKey("feedback.preAudit.enabled");
		return "true".equals(value);
	}

	private String getDisplayContent(String content) {
		if (content == null || content.isEmpty()) {
			return content;
		}

		final int MAX_LINES = 4;
		// 关键假设：基于前端UI估算，平均每行能显示的字符数。
		// 这是纯后端实现多行截断的必要妥协，可以根据前端实际效果进行微调。 Iphone13 一行大概20个字
		final int CHARS_PER_LINE = 20;

		int lineCount = 1;
		int charsOnCurrentLine = 0;

		for (int i = 0; i < content.length(); i++) {
			char c = content.charAt(i);

			if (c == '\n') {
				lineCount++;
				charsOnCurrentLine = 0;
			} else {
				charsOnCurrentLine++;
				if (charsOnCurrentLine > CHARS_PER_LINE) {
					lineCount++;
					// 这一行超出的第一个字符，本身就是下一行第一个字符
					charsOnCurrentLine = 1;
				}
			}

			// 检查是否超过最大行数
			if (lineCount > MAX_LINES) {
				// 字符i是导致超出的第一个字符，所以我们截取它之前的内容
				return content.substring(0, i) + "...";
			}
		}

		// 如果循环正常结束，说明全文未超过限制，返回完整内容
		return content;
	}

	private Integer getContactAccessCount() {
		return Integer.parseInt(paramService.getByKey("contact.limit"));
	}
}
