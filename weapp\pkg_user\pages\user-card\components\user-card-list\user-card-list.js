// 引入名片管理 Store
const businessCardStore = require('../../../../../stores/businessCardStore.js');

Component({
  properties: {
    cardList: {
      type: Array,
      value: []
    }
  },
  data: {
    flippedIndex: -1 // 当前反转的卡片索引
  },
  methods: {
    onCardTap(e) {
      const index = e.currentTarget.dataset.index;
      this.setData({
        flippedIndex: this.data.flippedIndex === index ? -1 : index
      });
    },

    // 跳转到添加名片页面
    onAddCard() {
      wx.navigateTo({
        url: '/pkg_user/pages/user-card/add-cardInfo/add-cardInfo'
      });
    },

    // 隐藏卡片（暂时保持原有逻辑，可以后续扩展为修改可见性状态）
    onHideCard(e) {
      const index = e.currentTarget.dataset.index;
      const card = this.data.cardList[index];

      wx.showModal({
        title: '提示',
        content: '确定要隐藏这张名片吗？',
        success: (res) => {
          if (res.confirm) {
            // 触发父组件事件，从列表中移除该卡片
            this.triggerEvent('hideCard', {
              index: index,
              cardId: card.cardId || card.id
            });

            wx.showToast({
              title: '已隐藏',
              icon: 'success'
            });
          }
        }
      });
    },

    // 编辑卡片
    onEditCard(e) {
      const index = e.currentTarget.dataset.index;
      const card = this.data.cardList[index];

      // 跳转到编辑页面，传递卡片ID
      wx.navigateTo({
        url: `/pkg_user/pages/user-card/add-cardInfo/add-cardInfo?mode=edit&cardId=${card.cardId || card.id}`
      });
    },

    // 删除卡片
    async onDeleteCard(e) {
      const index = e.currentTarget.dataset.index;
      const card = this.data.cardList[index];
      const cardId = card.cardId || card.id;

      if (!cardId) {
        wx.showToast({
          title: '卡片ID不存在',
          icon: 'none'
        });
        return;
      }

      wx.showModal({
        title: '确认删除',
        content: `确定要删除"${card.company || card.name}"的名片吗？此操作不可恢复。`,
        confirmColor: '#ff8080',
        success: async (res) => {
          if (res.confirm) {
            // 显示加载状态
            wx.showLoading({
              title: '删除中...'
            });

            try {
              // 调用删除API
              const result = await businessCardStore.deleteCard(cardId.toString());

              wx.hideLoading();

              if (result.success) {
                // 删除成功，触发父组件事件更新列表
                this.triggerEvent('deleteCard', {
                  index: index,
                  cardId: cardId
                });

                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
              } else {
                wx.showToast({
                  title: result.message || '删除失败',
                  icon: 'none'
                });
              }
            } catch (error) {
              wx.hideLoading();
              console.error('删除名片失败:', error);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    }
  }
});