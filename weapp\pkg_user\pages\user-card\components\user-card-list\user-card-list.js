Component({
  properties: {
    cardList: {
      type: Array,
      value: []
    }
  },
  data: {
    flippedIndex: -1 // 当前反转的卡片索引
  },
  methods: {
    onCardTap(e) {
      const index = e.currentTarget.dataset.index;
      this.setData({
        flippedIndex: this.data.flippedIndex === index ? -1 : index
      });
    },
    
    // 跳转到添加名片页面
    onAddCard() {
      wx.navigateTo({
        url: '/pkg_user/pages/add-cardInfo/add-cardInfo'
      });
    },
    
    // 隐藏卡片
    onHideCard(e) {
      const index = e.currentTarget.dataset.index;
      wx.showModal({
        title: '提示',
        content: '确定要隐藏这张名片吗？',
        success: (res) => {
          if (res.confirm) {
            // 这里可以调用API隐藏卡片
            wx.showToast({
              title: '已隐藏',
              icon: 'success'
            });
          }
        }
      });
    },
    
    // 编辑卡片
    onEditCard(e) {
      const index = e.currentTarget.dataset.index;
      const card = this.data.cardList[index];
      wx.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      });
      // 这里可以跳转到编辑页面
      // wx.navigateTo({
      //   url: `/pkg_user/pages/edit-card/edit-card?cardId=${card.cardId}`
      // });
    },
    
    // 删除卡片
    onDeleteCard(e) {
      const index = e.currentTarget.dataset.index;
      const card = this.data.cardList[index];
      wx.showModal({
        title: '确认删除',
        content: `确定要删除"${card.company}"的名片吗？此操作不可恢复。`,
        confirmColor: '#ff8080',
        success: (res) => {
          if (res.confirm) {
            // 这里可以调用API删除卡片
            wx.showToast({
              title: '已删除',
              icon: 'success'
            });
          }
        }
      });
    }
  }
});