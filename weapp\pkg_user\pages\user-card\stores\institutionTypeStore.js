const { request } = require('../utils/request.js');
const { institutionType: API } = require('../config/api.js');

/**
 * 获取机构分类列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码，默认1
 * @param {number} params.size 每页大小，默认100（获取所有分类）
 * @returns {Promise<Array>} 机构分类列表数据
 */
const getInstitutionTypeList = async (params = {}) => {
  try {
    console.log('=== institutionTypeStore: 开始请求机构分类列表 ===');
    console.log('institutionTypeStore: API.list =', API.list);
    console.log('institutionTypeStore: 请求参数 =', JSON.stringify(params, null, 2));

    const startTime = Date.now();
    const response = await request({
      url: API.list,
      method: 'GET'
    });
    const endTime = Date.now();

    console.log(`institutionTypeStore: 请求耗时 ${endTime - startTime}ms`);
    console.log('institutionTypeStore: 接口响应状态 =', {
      code: response.code,
      success: response.success,
      msg: response.msg
    });
    console.log('institutionTypeStore: 接口响应数据 =', JSON.stringify(response.data, null, 2));

    if (response.code === 200) {
      const data = response.data;
      // 后端直接返回数组，不是分页对象
      const processedData = processInstitutionTypes(Array.isArray(data) ? data : []);
      console.log('institutionTypeStore: 处理后的分类数据 =', processedData.length, '个分类');
      return processedData;
    } else {
      console.error('institutionTypeStore: 获取机构分类列表失败 - 响应码:', response.code);
      console.error('institutionTypeStore: 错误消息:', response.msg);
      console.log('institutionTypeStore: 使用默认分类数据');
      return getDefaultInstitutionTypes();
    }
  } catch (error) {
    console.error('=== institutionTypeStore: 请求机构分类列表接口失败 ===');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    console.error('错误堆栈:', error.stack);
    console.error('完整错误对象:', error);
    console.log('institutionTypeStore: 使用默认分类数据');
    return getDefaultInstitutionTypes();
  }
};

/**
 * 处理机构分类数据
 * @param {Array} types 原始分类数据
 * @returns {Array} 处理后的分类数据
 */
const processInstitutionTypes = (types) => {
  if (!types || !Array.isArray(types)) {
    return getDefaultInstitutionTypes();
  }

  const processedTypes = types
    .filter(type => type.status === 1) // 只显示启用的分类
    .map(type => ({
      id: type.id,
      name: type.name || '未知分类',
      icon: type.icon || '/assets/images/institution-types/default.png',
      sortOrder: type.sortOrder || 0
    }))
    .sort((a, b) => a.sortOrder - b.sortOrder); // 按排序字段排序

  // 在开头添加"全部"选项
  return [
    { id: 'all', name: '全部', icon: '/assets/images/institution-types/all.png', sortOrder: -1 },
    ...processedTypes
  ];
};

/**
 * 获取默认机构分类数据（当接口失败时使用）
 * @returns {Array} 默认分类数据
 */
const getDefaultInstitutionTypes = () => {
  return [
    { id: 'all', name: '全部', icon: '/assets/images/institution-types/all.png', sortOrder: -1 },
    { id: 'education', name: '教育培训', icon: '/assets/images/institution-types/education.png', sortOrder: 1 },
    { id: 'medical', name: '医疗健康', icon: '/assets/images/institution-types/medical.png', sortOrder: 2 },
    { id: 'finance', name: '金融服务', icon: '/assets/images/institution-types/finance.png', sortOrder: 3 },
    { id: 'government', name: '政府机构', icon: '/assets/images/institution-types/government.png', sortOrder: 4 },
    { id: 'business', name: '商业服务', icon: '/assets/images/institution-types/business.png', sortOrder: 5 },
    { id: 'catering', name: '餐饮美食', icon: '/assets/images/institution-types/catering.png', sortOrder: 6 },
    { id: 'entertainment', name: '休闲娱乐', icon: '/assets/images/institution-types/entertainment.png', sortOrder: 7 },
    { id: 'shopping', name: '购物消费', icon: '/assets/images/institution-types/shopping.png', sortOrder: 8 },
    { id: 'life', name: '生活服务', icon: '/assets/images/institution-types/life.png', sortOrder: 9 },
    { id: 'other', name: '其他', icon: '/assets/images/institution-types/other.png', sortOrder: 99 }
  ];
};

/**
 * 根据分类ID获取分类名称
 * @param {string|number} typeId 分类ID
 * @param {Array} types 分类列表
 * @returns {string} 分类名称
 */
const getInstitutionTypeName = (typeId, types = []) => {
  if (!typeId || typeId === 'all') {
    return '全部';
  }

  const type = types.find(t => t.id === typeId);
  return type ? type.name : '未知分类';
};

/**
 * 根据分类名称获取分类ID
 * @param {string} typeName 分类名称
 * @param {Array} types 分类列表
 * @returns {string|number} 分类ID
 */
const getInstitutionTypeId = (typeName, types = []) => {
  if (!typeName || typeName === '全部') {
    return 'all';
  }

  const type = types.find(t => t.name === typeName);
  return type ? type.id : 'all';
};

/**
 * 获取分类图标
 * @param {string|number} typeId 分类ID
 * @param {Array} types 分类列表
 * @returns {string} 分类图标URL
 */
const getInstitutionTypeIcon = (typeId, types = []) => {
  if (!typeId || typeId === 'all') {
    return '/assets/images/institution-types/all.png';
  }

  const type = types.find(t => t.id === typeId);
  return type ? type.icon : '/assets/images/institution-types/default.png';
};

module.exports = {
  getInstitutionTypeList,
  processInstitutionTypes,
  getDefaultInstitutionTypes,
  getInstitutionTypeName,
  getInstitutionTypeId,
  getInstitutionTypeIcon
};
