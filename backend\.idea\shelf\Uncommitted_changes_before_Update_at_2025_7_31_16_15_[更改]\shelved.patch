Index: ../weapp/pages/local/local.wxss
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* pkg_user/pages/local/local.wxss */\r\n\r\n/* 页面整体 */\r\npage {\r\n  height: 100vh;\r\n  background: #FF7B7B;\r\n  overflow: hidden; /* 禁止页面整体滚动 */\r\n}\r\n\r\n/* 滚动容器 */\r\n.scroll-container {\r\n  position: relative;\r\n  background: #FF7B7B;\r\n  box-sizing: border-box;\r\n  height: 100vh;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  scrollbar-width: none; /* Firefox */\r\n}\r\n\r\n.scroll-container::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  position: relative;\r\n  z-index: 1;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  min-height: calc(100vh - 100rpx); /* 确保内容区域有足够高度 */\r\n  background: #f5f5f5; /* 内容区域保持浅灰色背景 */\r\n}\r\n\r\n/* 轮播图容器样式 */\r\n.banner-container {\r\n  position: relative;\r\n  margin: 0 -20rpx 30rpx -20rpx; /* 负边距让轮播图延伸到屏幕边缘 */\r\n  z-index: 1;\r\n}\r\n\r\n/* 轮播图上方渐变效果 - 与导航栏融合 */\r\n.banner-container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 100rpx;\r\n  background: linear-gradient(to bottom, rgba(255, 107, 107, 0.6), transparent);\r\n  pointer-events: none;\r\n  z-index: 2;\r\n}\r\n\r\n/* 轮播图下方渐变效果 */\r\n.banner-container::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 80rpx;\r\n  background: linear-gradient(to top, #f5f5f5, transparent);\r\n  pointer-events: none;\r\n  z-index: 2;\r\n}\r\n\r\n/* 禁用组件自带的渐变效果，使用页面级别的渐变 */\r\n.banner-container banner-swiper .top-banner::before,\r\n.banner-container banner-swiper .top-banner::after {\r\n  display: none;\r\n}\r\n\r\n/* 确保轮播图内容正确显示 */\r\n.banner-container banner-swiper {\r\n  display: block;\r\n  width: 100%;\r\n}\r\n\r\n.refresh-text {\r\n  position: relative;\r\n  z-index: 2;\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  margin-top: 40rpx;\r\n  opacity: 0.85;\r\n}\r\n\r\n/* 下拉刷新圆环 loading 动画样式 */\r\n.refresh-loading {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  background: #ff6b6b; /* 主题色背景 */\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.circle-loader {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border: 4rpx solid #fff;\r\n  border-top: 4rpx solid #fff;\r\n  border-right: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #ff6b6b;\r\n  border-left: 4rpx solid #ff6b6b;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 8rpx;\r\n  box-shadow: 0 0 8rpx #ff6b6b33;\r\n  background: transparent;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg);}\r\n  100% { transform: rotate(360deg);}\r\n}\r\n\r\n.refresh-text {\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  opacity: 0.85;\r\n  text-shadow: 0 2rpx 8rpx #ff6b6b33;\r\n}\r\n\r\n.refresh-coins {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 10;\r\n  background: #ff6b6b;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  pointer-events: none; /* 不影响下方内容交互 */\r\n}\r\n\r\n.coin {\r\n  position: absolute;\r\n  top: -40rpx;\r\n  animation: coinDrop 0.9s cubic-bezier(0.4,1.4,0.6,1) forwards;\r\n  transform: rotate(var(--coin-rotate, 0deg));\r\n}\r\n\r\n@keyframes coinDrop {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateY(-30rpx) scale(0.7) rotate(var(--coin-rotate, 0deg));\r\n  }\r\n  40% {\r\n    opacity: 1;\r\n    transform: translateY(40rpx) translateX(calc(var(--coin-swing, 0vw) * 0.5)) scale(1.1) rotate(calc(var(--coin-rotate, 0deg) * 0.7));\r\n  }\r\n  70% {\r\n    transform: translateY(70rpx) translateX(var(--coin-swing, 0vw)) scale(0.95) rotate(calc(var(--coin-rotate, 0deg) * 0.3));\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: translateY(80rpx) translateX(var(--coin-swing, 0vw)) scale(1) rotate(0deg);\r\n  }\r\n}\r\n\r\n.refresh-text {\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  opacity: 0.85;\r\n  text-shadow: 0 2rpx 8rpx #ff6b6b33;\r\n  position: relative;\r\n  z-index: 2;\r\n  margin-top: 40rpx;\r\n}\r\n\r\n.refresh-coins-fixed {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  height: 160rpx;\r\n  z-index: 9999;\r\n  background: #FF7B7B;\r\n  pointer-events: none;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.refresh-bottom-area {\r\n  width: 100vw;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  background: #FF7B7B;\r\n  padding-top: 12rpx;\r\n  padding-bottom: 18rpx;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.coin-tray {\r\n  width: 80rpx;\r\n  height: 18rpx;\r\n  background: linear-gradient(90deg, #ffb86b 0%, #ffe066 100%);\r\n  border-radius: 0 0 40rpx 40rpx / 0 0 18rpx 18rpx;\r\n  box-shadow: 0 4rpx 12rpx #ffb86b55;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.refresh-text {\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  opacity: 0.95;\r\n  text-shadow: 0 2rpx 8rpx #ff6b6b33;\r\n  margin-top: 2rpx;\r\n  text-align: center;\r\n}\r\n\r\n.coin {\r\n  position: absolute;\r\n  top: 20rpx;\r\n  animation: coinDropFixed 1.1s cubic-bezier(0.4,1.4,0.6,1) forwards;\r\n  transform: rotate(var(--coin-rotate, 0deg));\r\n}\r\n\r\n@keyframes coinDropFixed {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateY(-40rpx) scale(0.7) rotate(var(--coin-rotate, 0deg));\r\n  }\r\n  40% {\r\n    opacity: 1;\r\n    transform: translateY(60rpx) translateX(calc(var(--coin-swing, 0vw) * 0.5)) scale(1.1) rotate(calc(var(--coin-rotate, 0deg) * 0.7));\r\n  }\r\n  70% {\r\n    transform: translateY(110rpx) translateX(var(--coin-swing, 0vw)) scale(0.95) rotate(calc(var(--coin-rotate, 0deg) * 0.3));\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: translateY(120rpx) translateX(var(--coin-swing, 0vw)) scale(1) rotate(0deg);\r\n  }\r\n} \r\n\r\n/* 吸顶分类栏 */\r\n.sticky-category-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #eee;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 刷新动画已移除 */\r\n\r\n/* 主Tab区域样式 */\r\n.main-tab-section {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 20rpx 30rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.main-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.main-tab-item {\r\n  padding: 16rpx 0;\r\n  margin-right: 40rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  color: #666;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.main-tab-item:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n.main-tab-item.active {\r\n  color: #ff6b6b;\r\n  font-weight: 600;\r\n}\r\n\r\n.main-tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -8rpx;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4rpx;\r\n  background: linear-gradient(90deg, #ff6b6b, #ff8585);\r\n  border-radius: 2rpx;\r\n  animation: slideIn 0.3s ease;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: scaleX(0);\r\n  }\r\n  to {\r\n    transform: scaleX(1);\r\n  }\r\n}\r\n\r\n/* 申请入驻按钮 */\r\n.apply-settle-btn {\r\n  padding: 12rpx 24rpx;\r\n  background: linear-gradient(135deg, #ff6b6b, #ff8585);\r\n  color: #fff;\r\n  font-size: 26rpx;\r\n  font-weight: 500;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.apply-settle-btn:active {\r\n  transform: scale(0.95);\r\n  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);\r\n}\r\n\r\n/* 卡片区域 - 参考index页面样式 */\r\n.card-section {\r\n  background-color: var(--card-background, #fff);\r\n  border-radius: 24rpx;\r\n  margin: 0; /* 移除外边距，让内容填满 */\r\n  overflow: hidden;\r\n}\r\n\r\n.card-section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 30rpx 0 20rpx;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.title-icon {\r\n  font-size: 32rpx;\r\n  margin-right: 12rpx;\r\n}\r\n\r\n.title-text {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n/* 分类标签 */\r\n.category-tabs-scroll {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.category-tabs {\r\n  display: flex;\r\n  padding: 0 10rpx;\r\n}\r\n\r\n.category-tabs.single-tab {\r\n  justify-content: center;\r\n}\r\n\r\n.tab-item {\r\n  flex-shrink: 0;\r\n  padding: 16rpx 28rpx;\r\n  margin: 0 12rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 24rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  text-align: center;\r\n  white-space: nowrap;\r\n  border: 2rpx solid transparent;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-item.active {\r\n  background: linear-gradient(135deg, #ff6b6b, #ff8585);\r\n  color: #fff;\r\n  transform: scale(1.05);\r\n  border-color: rgba(255, 107, 107, 0.3);\r\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.25);\r\n}\r\n\r\n.tab-item:first-child {\r\n  margin-left: 0;\r\n}\r\n\r\n.tab-item:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n/* 机构列表容器 */\r\n.institutions-container {\r\n  background-color: #f5f6f7;\r\n  padding: 0;\r\n}\r\n\r\n/* 底部状态区域 */\r\n.bottom-status {\r\n  padding: 40rpx 0;\r\n  text-align: center;\r\n}\r\n\r\n.loading-more {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.loading-more.preloading {\r\n  color: #ff6b6b;\r\n}\r\n\r\n.reached-bottom,\r\n.no-more {\r\n  font-size: 26rpx;\r\n  color: #999;\r\n  padding: 20rpx;\r\n  background: #f8f8f8;\r\n  border-radius: 12rpx;\r\n  margin: 0 20rpx;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 120rpx 40rpx;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin: 20rpx;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 120rpx;\r\n  margin-bottom: 30rpx;\r\n  opacity: 0.6;\r\n  animation: float 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-10rpx);\r\n  }\r\n}\r\n\r\n.empty-text {\r\n  font-size: 32rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.empty-tip {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n  text-align: center;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 旧样式已移除，使用组件化设计 */\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/../weapp/pages/local/local.wxss b/../weapp/pages/local/local.wxss
--- a/../weapp/pages/local/local.wxss	(revision 368e288bb1d8f36975596a0ac8d74ab39550fa9e)
+++ b/../weapp/pages/local/local.wxss	(date 1753943535145)
@@ -410,7 +410,6 @@
 
 /* 机构列表容器 */
 .institutions-container {
-  background-color: #f5f6f7;
   padding: 0;
 }
 
