const { request } = require('../utils/request.js');

/**
 * 积分管理Store
 */
class PointsStore {
  /**
   * 获取用户积分信息
   */
  async getUserPoints() {
    try {
      const result = await request({
        url: '/miniapp/points/info',
        method: 'GET'
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取积分信息失败');
      }
    } catch (error) {
      console.error('获取积分信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取积分记录
   * @param {number} current 当前页码
   * @param {number} size 每页大小
   */
  async getPointsRecords(current = 1, size = 20) {
    try {
      const result = await request({
        url: '/miniapp/points/records',
        method: 'GET',
        data: { current, size }
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取积分记录失败');
      }
    } catch (error) {
      console.error('获取积分记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据类型获取积分记录
   * @param {string} type 积分类型
   * @param {number} current 当前页码
   * @param {number} size 每页大小
   */
  async getPointsRecordsByType(type, current = 1, size = 20) {
    try {
      const result = await request({
        url: '/miniapp/points/records/type',
        method: 'GET',
        data: { type, current, size }
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取积分记录失败');
      }
    } catch (error) {
      console.error('获取积分记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取积分统计信息
   * @param {string} startDate 开始日期
   * @param {string} endDate 结束日期
   */
  async getPointsStatistics(startDate, endDate) {
    try {
      const params = {};
      if (startDate) params.startDate = startDate;
      if (endDate) params.endDate = endDate;
      
      const result = await request({
        url: '/miniapp/points/statistics',
        method: 'GET',
        data: params
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取积分统计失败');
      }
    } catch (error) {
      console.error('获取积分统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取积分明细
   * @param {Object} params 查询参数
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   * @param {string} params.type 积分类型（可选）
   */
  async getPointsDetail(params = {}) {
    try {
      const result = await request({
        url: '/blade-chat/points/detail',
        method: 'GET',
        data: params
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取积分明细失败');
      }
    } catch (error) {
      console.error('获取积分明细失败:', error);
      throw error;
    }
  }

  /**
   * 获取积分商城商品列表
   * @param {Object} params 查询参数
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   * @param {string} params.category 商品分类（可选）
   */
  async getPointsMall(params = {}) {
    try {
      const result = await request({
        url: '/blade-chat/points/mall',
        method: 'GET',
        data: params
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取积分商城失败');
      }
    } catch (error) {
      console.error('获取积分商城失败:', error);
      throw error;
    }
  }

  /**
   * 兑换商品
   * @param {Object} params 兑换参数
   * @param {number} params.goodsId 商品ID
   * @param {number} params.quantity 兑换数量
   */
  async exchangeGoods(params) {
    try {
      const result = await request({
        url: '/blade-chat/points/exchange',
        method: 'POST',
        data: params
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '兑换失败');
      }
    } catch (error) {
      console.error('兑换失败:', error);
      throw error;
    }
  }

  /**
   * 获取兑换记录
   * @param {Object} params 查询参数
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   */
  async getExchangeRecord(params = {}) {
    try {
      const result = await request({
        url: '/blade-chat/points/exchange/record',
        method: 'GET',
        data: params
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '获取兑换记录失败');
      }
    } catch (error) {
      console.error('获取兑换记录失败:', error);
      throw error;
    }
  }

  /**
   * 分享获得积分
   */
  async shareForPoints() {
    try {
      const result = await request({
        url: '/blade-chat/points/share',
        method: 'POST'
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '分享积分获取失败');
      }
    } catch (error) {
      console.error('分享积分获取失败:', error);
      throw error;
    }
  }

  /**
   * 邀请好友获得积分
   * @param {string} inviteCode 邀请码
   */
  async inviteForPoints(inviteCode) {
    try {
      const result = await request({
        url: '/blade-chat/points/invite',
        method: 'POST',
        data: { inviteCode }
      });

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '邀请积分获取失败');
      }
    } catch (error) {
      console.error('邀请积分获取失败:', error);
      throw error;
    }
  }
}

module.exports = new PointsStore(); 