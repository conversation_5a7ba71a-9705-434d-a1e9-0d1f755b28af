package org.springblade.miniapp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.post.vo.SupPostVO;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.service.IBusinessCardService;
import org.springblade.business.user.vo.BusinessCardVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.miniapp.service.WeChatUserService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/card")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序个人名片接口")
public class WeChatBusinessCardController {

	@Resource
	private final IBusinessCardService businessCardService;
	/**
	 * 新增 名片信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入businessCard")
	public R save(@Valid @RequestBody BusinessCard businessCard) {
		// 设置当前用户ID
		businessCard.setCreateUser(AuthUtil.getUserId());
		return R.status(businessCardService.save(businessCard));
	}

	/**
	 * 修改 名片信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入businessCard")
	public R update(@Valid @RequestBody BusinessCard businessCard) {
		// 确保只能修改自己的名片
		Long userId = AuthUtil.getUserId();
		BusinessCard existingCard = businessCardService.getById(businessCard.getId());
		if (existingCard == null || !userId.equals(existingCard.getCreateUser())) {
			return R.fail("无权限修改此名片");
		}
		businessCard.setUpdateUser(userId);
		return R.status(businessCardService.updateById(businessCard));
	}

	/**
	 * 新增或修改 名片信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入businessCard")
	public R submit(@Valid @RequestBody BusinessCard businessCard) {
		Long userId = AuthUtil.getUserId();

		if (businessCard.getId() != null) {
			// 修改操作 - 检查权限
			BusinessCard existingCard = businessCardService.getById(businessCard.getId());
			if (existingCard == null || !userId.equals(existingCard.getCreateUser())) {
				return R.fail("无权限修改此名片");
			}
			businessCard.setUpdateUser(userId);
		} else {
			// 新增操作 - 检查是否已有名片
			BusinessCard query = new BusinessCard();
			query.setCreateUser(userId);
			BusinessCard existingCard = businessCardService.getOne(Condition.getQueryWrapper(query));
			if (existingCard != null) {
				// 如果已有名片，则更新现有名片
				businessCard.setId(existingCard.getId());
				businessCard.setUpdateUser(userId);
			} else {
				// 新建名片
				businessCard.setCreateUser(userId);
			}
		}

		return R.status(businessCardService.saveOrUpdate(businessCard));
	}


	/**
	 * 删除 名片信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(businessCardService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 获取当前用户名片
	 */
	@GetMapping("/my-card")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "获取当前用户名片", description = "获取当前登录用户的名片信息")
	public R<List<BusinessCard>> getMyCard() {
		Long userId = AuthUtil.getUserId();
		BusinessCard query = new BusinessCard();
		query.setCreateUser(userId);

		List<BusinessCard> myCard = businessCardService.list(Condition.getQueryWrapper(query));
		return R.data(myCard);
	}

	/**
	 * 分页查询名片列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "分页查询名片列表", description = "分页查询名片信息")
	public R<IPage<BusinessCardVO>> getCardList(BusinessCardVO businessCard, Query query) {
		IPage<BusinessCardVO> pages = businessCardService.selectBusinessCardPage(Condition.getPage(query), businessCard);
		return R.data(pages);
	}

	/**
	 * 获取名片详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "获取名片详情", description = "根据ID获取名片详细信息")
	public R<BusinessCardVO> getCardDetail(@Parameter(description = "名片ID", required = true) @RequestParam Long id) {
		BusinessCardVO businessCard = new BusinessCardVO();
		businessCard.setId(id);
		BusinessCardVO detail = businessCardService.getDetail(businessCard);
		return R.data(detail);
	}
}
