package org.springblade.miniapp.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.business.user.entity.BusinessCard;
import org.springblade.business.user.service.IBusinessCardService;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/card")
@io.swagger.v3.oas.annotations.tags.Tag(name = "小程序个人名片接口")
public class WeChatBusinessCardController {

	@Resource
	private final IBusinessCardService businessCardService;
	/**
	 * 新增 名片信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入businessCard")
	public R save(@Valid @RequestBody BusinessCard businessCard) {
		// 设置当前用户ID
		businessCard.setCreateUser(AuthUtil.getUserId());
		return R.status(businessCardService.save(businessCard));
	}

	/**
	 * 修改 名片信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入businessCard")
	public R update(@Valid @RequestBody BusinessCard businessCard) {
		// 确保只能修改自己的名片
		Long userId = AuthUtil.getUserId();
		BusinessCard existingCard = businessCardService.getById(businessCard.getId());
		if (existingCard == null || !userId.equals(existingCard.getCreateUser())) {
			return R.fail("无权限修改此名片");
		}
		businessCard.setUpdateUser(userId);
		return R.status(businessCardService.updateById(businessCard));
	}

	/**
	 * 新增或修改 名片信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入businessCard")
	public R submit(@Valid @RequestBody BusinessCard businessCard) {
		Long userId = AuthUtil.getUserId();

		if (businessCard.getId() != null) {
			// 修改操作 - 检查权限
			BusinessCard existingCard = businessCardService.getById(businessCard.getId());
			if (existingCard == null || !userId.equals(existingCard.getCreateUser())) {
				return R.fail("无权限修改此名片");
			}
			businessCard.setUpdateUser(userId);
		} else {
			// 新增操作 - 检查是否已有名片
			BusinessCard query = new BusinessCard();
			query.setCreateUser(userId);
			BusinessCard existingCard = businessCardService.getOne(Condition.getQueryWrapper(query));
			if (existingCard != null) {
				// 如果已有名片，则更新现有名片
				businessCard.setId(existingCard.getId());
				businessCard.setUpdateUser(userId);
			} else {
				// 新建名片
				businessCard.setCreateUser(userId);
			}
		}

		return R.status(businessCardService.saveOrUpdate(businessCard));
	}


	/**
	 * 删除 名片信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(businessCardService.deleteLogic(Func.toLongList(ids)));
	}
}
